# /f:/水利站/backend/state_monitor.py
import datetime
import threading
import time
from typing import Dict, List, Optional
from database import SessionLocal
import crud
from data_store import DEVICE_DATA, DATA_LOCK, CONNECTED_CLIENTS, CLIENTS_LOCK
from device_control import create_do_command, send_command_to_device
from device_timing import wait_for_device_ready

# 状态监控配置
STATE_MONITOR_CONFIG = {
    "enabled": True,
    "check_interval_seconds": 60,  # 检查间隔（秒）
    "auto_repair_enabled": True,  # 是否启用自动修复
    "repair_retry_count": 3,  # 修复重试次数
    "repair_retry_delay": 10,  # 修复重试延时（秒）
    "float_timeout_check": True,  # 是否检查浮球超时
    "max_pump_runtime_hours": 3,  # 最大水泵运行时间（小时）
}

# 水泵DO映射
PUMP_TO_DO_MAPPING = {
    "water_pump1": "DO21",
    "water_pump2": "DO22",
    "air_pump1": "DO23",
    "air_pump2": "DO24",
}

PUMP_TO_DO_STATUS_MAPPING = {
    "water_pump1": "DO21_status",
    "water_pump2": "DO22_status",
    "air_pump1": "DO23_status",
    "air_pump2": "DO24_status",
}


class StateMonitor:
    def __init__(self):
        self.running = False
        self.monitor_thread = None
        self._stop_event = threading.Event()

    def start(self):
        """启动状态监控"""
        if self.running:
            return

        self.running = True
        self._stop_event.clear()
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] [state_monitor] 状态监控已启动")

    def stop(self):
        """停止状态监控"""
        if not self.running:
            return

        self.running = False
        self._stop_event.set()

        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] [state_monitor] 状态监控已停止")

    def _monitor_loop(self):
        """监控主循环"""
        while self.running and not self._stop_event.is_set():
            try:
                if STATE_MONITOR_CONFIG["enabled"]:
                    self._perform_state_check()

                # 等待下一次检查
                self._stop_event.wait(STATE_MONITOR_CONFIG["check_interval_seconds"])

            except Exception as e:
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{current_time}] [state_monitor] 监控循环发生错误: {e}")
                self._stop_event.wait(30)  # 错误后等待30秒再继续

    def _perform_state_check(self):
        """执行状态检查"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 获取所有连接的设备
        with CLIENTS_LOCK:
            connected_devices = list(CONNECTED_CLIENTS.keys())

        if not connected_devices:
            return

        print(
            f"[{current_time}] [state_monitor] 开始状态检查，连接设备: {connected_devices}"
        )

        db = SessionLocal()
        try:
            # 记录监控检查开始
            check_log = crud.create_operation_log(
                db=db,
                operation_type="state_monitor",
                operation_details=f"状态监控检查开始，设备数量: {len(connected_devices)}",
                execution_status="pending",
                additional_data={
                    "connected_devices": connected_devices,
                    "check_type": "scheduled",
                    "config": STATE_MONITOR_CONFIG.copy(),
                },
            )

            issues_found = []
            repairs_attempted = []

            for sn in connected_devices:
                device_issues = self._check_device_state(db, sn)
                if device_issues:
                    issues_found.extend(device_issues)

                    # 如果启用自动修复，尝试修复
                    if STATE_MONITOR_CONFIG["auto_repair_enabled"]:
                        repair_results = self._repair_device_state(
                            db, sn, device_issues
                        )
                        repairs_attempted.extend(repair_results)

            # 更新检查日志
            status = "success" if not issues_found else "issues_found"
            error_msg = f"发现 {len(issues_found)} 个问题" if issues_found else None

            crud.update_operation_log_status(db, check_log.id, status, error_msg)

            # 更新检查日志的详细信息
            if issues_found or repairs_attempted:
                check_log.additional_data.update(
                    {
                        "issues_found": issues_found,
                        "repairs_attempted": repairs_attempted,
                        "total_issues": len(issues_found),
                        "total_repairs": len(repairs_attempted),
                    }
                )
                db.commit()

            if issues_found:
                print(
                    f"[{current_time}] [state_monitor] 发现 {len(issues_found)} 个状态问题"
                )
                if repairs_attempted:
                    print(
                        f"[{current_time}] [state_monitor] 尝试修复 {len(repairs_attempted)} 个问题"
                    )

        except Exception as e:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{current_time}] [state_monitor] 状态检查发生错误: {e}")
        finally:
            db.close()

    def _check_device_state(self, db, sn: str) -> List[Dict]:
        """检查单个设备的状态"""
        issues = []
        
        # 跳过M300设备的状态监控（M300本地自主控制）
        if sn.startswith("02800125"):
            return issues

        # 获取数据库中的预期状态
        active_pump_key = f"active_pump_{sn}"
        start_time_key = f"pump_start_time_{sn}"
        active_air_pump_key = f"active_air_pump_{sn}"
        air_pump_start_time_key = f"air_pump_start_time_{sn}"

        expected_active_pump = crud.get_kv(db, active_pump_key)
        start_time_str = crud.get_kv(db, start_time_key)
        expected_active_air_pump = crud.get_kv(db, active_air_pump_key)
        air_pump_start_time_str = crud.get_kv(db, air_pump_start_time_key)

        # 获取设备实际状态
        with DATA_LOCK:
            device_data = DEVICE_DATA.get(sn, {})

        current_time = datetime.datetime.now()

        # 检查1: 预期有水泵运行但实际没有运行
        if expected_active_pump and expected_active_pump in PUMP_TO_DO_STATUS_MAPPING:
            expected_status_key = PUMP_TO_DO_STATUS_MAPPING[expected_active_pump]
            actual_status = device_data.get(expected_status_key)

            if actual_status != 1:
                issues.append(
                    {
                        "type": "pump_not_running",
                        "expected_pump": expected_active_pump,
                        "expected_status": 1,
                        "actual_status": actual_status,
                        "description": f"预期水泵 {expected_active_pump} 运行，但实际状态为 {actual_status}",
                    }
                )

        # 检查1B: 预期有气泵运行但实际没有运行
        if expected_active_air_pump and expected_active_air_pump in PUMP_TO_DO_STATUS_MAPPING:
            expected_status_key = PUMP_TO_DO_STATUS_MAPPING[expected_active_air_pump]
            actual_status = device_data.get(expected_status_key)

            if actual_status != 1:
                issues.append(
                    {
                        "type": "air_pump_not_running",
                        "expected_pump": expected_active_air_pump,
                        "expected_status": 1,
                        "actual_status": actual_status,
                        "description": f"预期气泵 {expected_active_air_pump} 运行，但实际状态为 {actual_status}",
                    }
                )

        # 检查2: 浮球状态与水泵运行状态不匹配 - 按新交替启动控制逻辑
        float1_status = device_data.get("float_switches", {}).get("float1")
        water_pump1_running = device_data.get("DO21_status") == 1
        water_pump2_running = device_data.get("DO22_status") == 1

        if float1_status == 1:  # 浮球激活
            # 按新逻辑：浮球激活时应该至少有一个水泵运行（交替启动）
            if not water_pump1_running and not water_pump2_running:
                issues.append(
                    {
                        "type": "float_active_no_pump",
                        "float1_status": float1_status,
                        "water_pump1_status": device_data.get("DO21_status"),
                        "water_pump2_status": device_data.get("DO22_status"),
                        "description": "浮球激活但没有水泵运行（应该交替启动一个水泵）",
                    }
                )

        elif float1_status == 0:  # 浮球未激活
            # 按新逻辑：浮球复位时应该关闭当前运行的水泵
            if water_pump1_running or water_pump2_running:
                issues.append(
                    {
                        "type": "float_inactive_pump_running",
                        "float1_status": float1_status,
                        "water_pump1_status": device_data.get("DO21_status"),
                        "water_pump2_status": device_data.get("DO22_status"),
                        "description": "浮球未激活但有水泵在运行（应该关闭运行的水泵）",
                    }
                )

        # 检查3: 水泵运行时间过长
        if (
            expected_active_pump
            and start_time_str
            and STATE_MONITOR_CONFIG["max_pump_runtime_hours"] > 0
        ):

            try:
                start_time = datetime.datetime.fromisoformat(start_time_str)
                runtime = current_time - start_time
                max_runtime = datetime.timedelta(
                    hours=STATE_MONITOR_CONFIG["max_pump_runtime_hours"]
                )

                if runtime > max_runtime:
                    issues.append(
                        {
                            "type": "pump_runtime_exceeded",
                            "pump_name": expected_active_pump,
                            "runtime_hours": runtime.total_seconds() / 3600,
                            "max_runtime_hours": STATE_MONITOR_CONFIG[
                                "max_pump_runtime_hours"
                            ],
                            "description": f"水泵 {expected_active_pump} 运行时间过长: {runtime.total_seconds() / 3600:.1f} 小时",
                        }
                    )
            except ValueError:
                pass  # 忽略时间格式错误

        # 检查3B: 气泵运行时间过长
        if (
            expected_active_air_pump
            and air_pump_start_time_str
            and STATE_MONITOR_CONFIG["max_pump_runtime_hours"] > 0
        ):

            try:
                start_time = datetime.datetime.fromisoformat(air_pump_start_time_str)
                runtime = current_time - start_time
                max_runtime = datetime.timedelta(
                    hours=STATE_MONITOR_CONFIG["max_pump_runtime_hours"]
                )

                if runtime > max_runtime:
                    issues.append(
                        {
                            "type": "air_pump_runtime_exceeded",
                            "pump_name": expected_active_air_pump,
                            "runtime_hours": runtime.total_seconds() / 3600,
                            "max_runtime_hours": STATE_MONITOR_CONFIG[
                                "max_pump_runtime_hours"
                            ],
                            "description": f"气泵 {expected_active_air_pump} 运行时间过长: {runtime.total_seconds() / 3600:.1f} 小时",
                        }
                    )
            except ValueError:
                pass  # 忽略时间格式错误

        # 检查4: 多个水泵同时运行 - 按新控制逻辑，双泵运行是合法的，不需要检查
        # 新控制逻辑允许：浮球激活时水泵1运行，超时后水泵2也可以运行
        # 因此删除多水泵运行的检查逻辑

        # 检查5: 气泵故障检测和自动切换检查
        air_pump1_auto = device_data.get("air_pump1", {}).get("auto_status") == 1
        air_pump2_auto = device_data.get("air_pump2", {}).get("auto_status") == 1
        air_pump1_running = device_data.get("DO23_status") == 1
        air_pump2_running = device_data.get("DO24_status") == 1
        
        # 检查气泵故障状态
        from pump_control_new import is_pump_faulty
        air_pump1_faulty = is_pump_faulty(device_data, "air_pump1")
        air_pump2_faulty = is_pump_faulty(device_data, "air_pump2")
        
        if air_pump1_auto and air_pump2_auto:
            # 两个气泵都在自动模式
            if air_pump1_faulty and not air_pump2_faulty:
                # 气泵1故障，气泵2正常，应确保气泵2运行
                if not air_pump2_running:
                    issues.append(
                        {
                            "type": "air_pump1_faulty_pump2_should_run",
                            "air_pump1_faulty": True,
                            "air_pump2_running": air_pump2_running,
                            "description": "气泵1故障，气泵2应该持续运行",
                        }
                    )
            elif air_pump2_faulty and not air_pump1_faulty:
                # 气泵2故障，气泵1正常，应确保气泵1运行
                if not air_pump1_running:
                    issues.append(
                        {
                            "type": "air_pump2_faulty_pump1_should_run",
                            "air_pump2_faulty": True,
                            "air_pump1_running": air_pump1_running,
                            "description": "气泵2故障，气泵1应该持续运行",
                        }
                    )
            elif not air_pump1_faulty and not air_pump2_faulty:
                # 两个气泵都正常，应该只有一个运行（按照"一个关闭时另一个运行"的逻辑）
                if not air_pump1_running and not air_pump2_running:
                    issues.append(
                        {
                            "type": "no_air_pump_running",
                            "air_pump1_running": air_pump1_running,
                            "air_pump2_running": air_pump2_running,
                            "description": "两个气泵都在自动模式但都没有运行",
                        }
                    )
        elif air_pump1_auto and not air_pump2_auto:
            # 只有气泵1在自动模式
            if not air_pump1_faulty and not air_pump1_running:
                issues.append(
                    {
                        "type": "air_pump1_auto_not_running",
                        "air_pump1_running": air_pump1_running,
                        "description": "气泵1在自动模式且正常但未运行",
                    }
                )
        elif air_pump2_auto and not air_pump1_auto:
            # 只有气泵2在自动模式
            if not air_pump2_faulty and not air_pump2_running:
                issues.append(
                    {
                        "type": "air_pump2_auto_not_running",
                        "air_pump2_running": air_pump2_running,
                        "description": "气泵2在自动模式且正常但未运行",
                    }
                )

        # 检查6: 多个气泵同时运行（仍需要检查，因为按逻辑只应有一个运行）
        air_pumps_running = []
        for pump_name, status_key in PUMP_TO_DO_STATUS_MAPPING.items():
            if pump_name.startswith("air_pump") and device_data.get(status_key) == 1:
                air_pumps_running.append(pump_name)

        if len(air_pumps_running) > 1:
            issues.append(
                {
                    "type": "multiple_air_pumps_running",
                    "pumps_running": air_pumps_running,
                    "description": f"多个气泵同时运行: {', '.join(air_pumps_running)}",
                }
            )

        return issues

    def _repair_device_state(self, db, sn: str, issues: List[Dict]) -> List[Dict]:
        """修复设备状态问题"""
        repairs = []

        for issue in issues:
            repair_result = self._repair_single_issue(db, sn, issue)
            if repair_result:
                repairs.append(repair_result)

        return repairs

    def _repair_single_issue(self, db, sn: str, issue: Dict) -> Optional[Dict]:
        """修复单个问题"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        issue_type = issue["type"]

        repair_log = crud.create_operation_log(
            db=db,
            operation_type="state_repair",
            operation_details=f"自动修复状态问题: {issue['description']}",
            device_sn=sn,
            execution_status="pending",
            additional_data={"issue": issue, "repair_type": issue_type},
        )

        try:
            if issue_type == "pump_not_running":
                # 重新启动预期运行的水泵
                return self._repair_pump_not_running(db, sn, issue, repair_log.id)

            elif issue_type == "air_pump_not_running":
                # 重新启动预期运行的气泵
                return self._repair_air_pump_not_running(db, sn, issue, repair_log.id)

            elif issue_type == "float_active_no_pump":
                # 浮球激活但没有水泵运行，启动下一个要使用的水泵
                return self._repair_float_active_no_pump(db, sn, issue, repair_log.id)

            elif issue_type == "air_pump1_faulty_pump2_should_run":
                # 气泵1故障，启动气泵2
                return self._repair_air_pump_failover(db, sn, "air_pump2", "DO24", repair_log.id)

            elif issue_type == "air_pump2_faulty_pump1_should_run":
                # 气泵2故障，启动气泵1
                return self._repair_air_pump_failover(db, sn, "air_pump1", "DO23", repair_log.id)

            elif issue_type == "no_air_pump_running":
                # 两个气泵都没运行，启动气泵1
                return self._repair_air_pump_failover(db, sn, "air_pump1", "DO23", repair_log.id)

            elif issue_type == "air_pump1_auto_not_running":
                # 气泵1自动模式未运行，启动气泵1
                return self._repair_air_pump_failover(db, sn, "air_pump1", "DO23", repair_log.id)

            elif issue_type == "air_pump2_auto_not_running":
                # 气泵2自动模式未运行，启动气泵2
                return self._repair_air_pump_failover(db, sn, "air_pump2", "DO24", repair_log.id)

            elif issue_type == "float_inactive_pump_running":
                # 浮球未激活但有水泵运行，停止所有水泵
                return self._repair_float_inactive_pump_running(
                    db, sn, issue, repair_log.id
                )

            elif issue_type == "multiple_air_pumps_running":
                # 多个气泵同时运行，停止除预期外的所有气泵
                return self._repair_multiple_air_pumps_running(db, sn, issue, repair_log.id)

            elif issue_type == "pump_runtime_exceeded":
                # 水泵运行时间过长，触发强制轮换
                return self._repair_pump_runtime_exceeded(db, sn, issue, repair_log.id)

            elif issue_type == "air_pump_runtime_exceeded":
                # 气泵运行时间过长，停止气泵
                return self._repair_air_pump_runtime_exceeded(db, sn, issue, repair_log.id)

        except Exception as e:
            error_msg = f"修复失败: {e}"
            print(f"[{current_time}] [state_monitor] {error_msg}")
            crud.update_operation_log_status(db, repair_log.id, "failed", error_msg)

        return None

    def _repair_pump_not_running(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Optional[Dict]:
        """修复水泵未运行问题"""
        pump_name = issue["expected_pump"]
        do_name = PUMP_TO_DO_MAPPING.get(pump_name)

        if not do_name:
            return None

        # 等待设备准备就绪
        wait_for_device_ready(sn)

        # 发送启动命令
        command = create_do_command(do_name=do_name, value=1)
        success, cmd_log_id = send_command_to_device(sn=sn, command=command)

        if success:
            crud.update_operation_log_status(db, log_id, "success")
            return {
                "repair_type": "pump_restart",
                "pump_name": pump_name,
                "do_name": do_name,
                "success": True,
            }
        else:
            crud.update_operation_log_status(db, log_id, "failed", "命令发送失败")
            return {
                "repair_type": "pump_restart",
                "pump_name": pump_name,
                "do_name": do_name,
                "success": False,
            }

    def _repair_float_active_no_pump(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Optional[Dict]:
        """修复浮球激活但没有水泵运行问题 - 按新交替启动逻辑"""
        # 获取下次要使用的水泵
        next_pump_key = f"next_pump_to_use_{sn}"
        next_pump = crud.get_kv(db, next_pump_key) or "water_pump1"

        # 检查目标水泵是否可用
        with DATA_LOCK:
            device_data = DEVICE_DATA.get(sn, {})

        # 检查是否故障和自动状态
        from pump_control_new import is_pump_faulty
        if next_pump == "water_pump1":
            if is_pump_faulty(device_data, "water_pump1") or device_data.get("water_pump1", {}).get("auto_status") != 1:
                next_pump = "water_pump2"
        
        if next_pump == "water_pump2":
            if is_pump_faulty(device_data, "water_pump2") or device_data.get("water_pump2", {}).get("auto_status") != 1:
                next_pump = "water_pump1"

        # 最终检查
        if next_pump == "water_pump1":
            if is_pump_faulty(device_data, "water_pump1") or device_data.get("water_pump1", {}).get("auto_status") != 1:
                crud.update_operation_log_status(db, log_id, "failed", "两个水泵都不可用")
                return None
            do_name = "DO21"
        else:
            if is_pump_faulty(device_data, "water_pump2") or device_data.get("water_pump2", {}).get("auto_status") != 1:
                crud.update_operation_log_status(db, log_id, "failed", "两个水泵都不可用")
                return None
            do_name = "DO22"

        # 启动目标水泵
        wait_for_device_ready(sn)
        command = create_do_command(do_name=do_name, value=1)
        success, _ = send_command_to_device(sn=sn, command=command)

        if success:
            # 更新数据库状态
            active_pump_key = f"active_pump_{sn}"
            start_time_key = f"pump_start_time_{sn}"
            current_time = datetime.datetime.now()

            crud.set_kv(db, active_pump_key, next_pump)
            crud.set_kv(db, start_time_key, current_time.isoformat())
            
            # 设置下次使用的水泵（交替）
            next_next_pump = "water_pump2" if next_pump == "water_pump1" else "water_pump1"
            crud.set_kv(db, next_pump_key, next_next_pump)

            crud.update_operation_log_status(db, log_id, "success")
            return {
                "repair_type": "float_pump_alternate_start",
                "pump_name": next_pump,
                "do_name": do_name,
                "success": True,
            }
        else:
            crud.update_operation_log_status(db, log_id, "failed", "命令发送失败")
            return {
                "repair_type": "float_pump_alternate_start",
                "pump_name": next_pump,
                "do_name": do_name,
                "success": False,
            }

    def _repair_air_pump_failover(
        self, db, sn: str, pump_name: str, do_name: str, log_id: int
    ) -> Optional[Dict]:
        """修复气泵故障切换问题"""
        # 启动指定的气泵
        wait_for_device_ready(sn)
        command = create_do_command(do_name=do_name, value=1)
        success, _ = send_command_to_device(sn=sn, command=command)

        if success:
            # 更新数据库状态
            active_air_pump_key = f"active_air_pump_{sn}"
            air_pump_start_time_key = f"air_pump_start_time_{sn}"
            current_time = datetime.datetime.now()

            crud.set_kv(db, active_air_pump_key, pump_name)
            crud.set_kv(db, air_pump_start_time_key, current_time.isoformat())

            crud.update_operation_log_status(db, log_id, "success")
            return {
                "repair_type": "air_pump_failover",
                "pump_name": pump_name,
                "do_name": do_name,
                "success": True,
            }
        else:
            crud.update_operation_log_status(db, log_id, "failed", "命令发送失败")
            return {
                "repair_type": "air_pump_failover",
                "pump_name": pump_name,
                "do_name": do_name,
                "success": False,
            }

    def _repair_air_pump_not_running(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Optional[Dict]:
        """修复气泵未运行问题"""
        pump_name = issue["expected_pump"]
        do_name = PUMP_TO_DO_MAPPING.get(pump_name)

        if not do_name:
            return None

        # 等待设备准备就绪
        wait_for_device_ready(sn)

        # 发送启动命令
        command = create_do_command(do_name=do_name, value=1)
        success, cmd_log_id = send_command_to_device(sn=sn, command=command)

        if success:
            crud.update_operation_log_status(db, log_id, "success")
            return {
                "repair_type": "air_pump_restart",
                "pump_name": pump_name,
                "do_name": do_name,
                "success": True,
            }
        else:
            crud.update_operation_log_status(db, log_id, "failed", "命令发送失败")
            return {
                "repair_type": "air_pump_restart",
                "pump_name": pump_name,
                "do_name": do_name,
                "success": False,
            }

    def _repair_pump2_without_record(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Optional[Dict]:
        """修复水泵2运行但无启动记录问题"""
        # 停止水泵2
        do_name = PUMP_TO_DO_MAPPING.get("water_pump2")
        if not do_name:
            return None

        wait_for_device_ready(sn)
        command = create_do_command(do_name=do_name, value=0)
        success, _ = send_command_to_device(sn=sn, command=command)

        if success:
            crud.update_operation_log_status(db, log_id, "success")
            return {
                "repair_type": "pump2_stop_without_record",
                "pump_name": "water_pump2",
                "do_name": do_name,
                "success": True,
            }
        else:
            crud.update_operation_log_status(db, log_id, "failed", "命令发送失败")
            return {
                "repair_type": "pump2_stop_without_record",
                "pump_name": "water_pump2", 
                "do_name": do_name,
                "success": False,
            }

    def _repair_float_inactive_pump_running(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Dict:
        """修复浮球未激活但有水泵运行问题 - 按新控制逻辑同时关闭所有水泵"""
        repairs_made = []

        # 按新逻辑：浮球复位时同时关闭两个水泵
        for pump_name in ["water_pump1", "water_pump2"]:
            status_key = PUMP_TO_DO_STATUS_MAPPING[pump_name]

            with DATA_LOCK:
                device_data = DEVICE_DATA.get(sn, {})

            if device_data.get(status_key) == 1:
                do_name = PUMP_TO_DO_MAPPING[pump_name]

                wait_for_device_ready(sn)
                command = create_do_command(do_name=do_name, value=0)
                success, _ = send_command_to_device(sn=sn, command=command)

                repairs_made.append(
                    {"pump_name": pump_name, "do_name": do_name, "success": success}
                )

        # 清理所有相关的数据库状态
        active_pump_key = f"active_pump_{sn}"
        start_time_key = f"pump_start_time_{sn}"
        
        crud.set_kv(db, active_pump_key, "")
        crud.set_kv(db, start_time_key, "")

        all_success = all(repair["success"] for repair in repairs_made)
        status = "success" if all_success else "partial_success"
        crud.update_operation_log_status(db, log_id, status)

        return {
            "repair_type": "float_all_pumps_stop",
            "repairs_made": repairs_made,
            "success": all_success,
        }

    def _repair_pump_runtime_exceeded(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Optional[Dict]:
        """修复水泵运行时间过长问题"""
        current_pump = issue["pump_name"]
        other_pump = "water_pump2" if current_pump == "water_pump1" else "water_pump1"

        # 检查另一个水泵是否可用
        with DATA_LOCK:
            device_data = DEVICE_DATA.get(sn, {})

        if device_data.get(other_pump, {}).get("auto_status") != 1:
            crud.update_operation_log_status(
                db, log_id, "failed", f"备用水泵 {other_pump} 未处于自动模式"
            )
            return None

        # 使用延时切换序列
        from device_timing import create_pump_sequence_with_delay

        create_pump_sequence_with_delay(
            sn=sn,
            stop_pump=current_pump,
            start_pump=other_pump,
            stop_log_id=log_id,
            start_log_id=log_id,
        )

        # 更新数据库状态
        active_pump_key = f"active_pump_{sn}"
        start_time_key = f"pump_start_time_{sn}"
        current_time = datetime.datetime.now()

        crud.set_kv(db, active_pump_key, other_pump)
        crud.set_kv(db, start_time_key, current_time.isoformat())

        crud.update_operation_log_status(db, log_id, "success")

        return {
            "repair_type": "runtime_switch",
            "old_pump": current_pump,
            "new_pump": other_pump,
            "runtime_hours": issue["runtime_hours"],
            "success": True,
        }

    def _repair_air_pump_not_running(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Optional[Dict]:
        """修复气泵未运行问题"""
        pump_name = issue["expected_pump"]
        do_name = PUMP_TO_DO_MAPPING.get(pump_name)

        if not do_name:
            return None

        # 等待设备准备就绪
        wait_for_device_ready(sn)

        # 发送启动命令
        command = create_do_command(do_name=do_name, value=1)
        success, cmd_log_id = send_command_to_device(sn=sn, command=command)

        if success:
            crud.update_operation_log_status(db, log_id, "success")
            return {
                "repair_type": "air_pump_restart",
                "pump_name": pump_name,
                "do_name": do_name,
                "success": True,
            }
        else:
            crud.update_operation_log_status(db, log_id, "failed", "命令发送失败")
            return {
                "repair_type": "air_pump_restart",
                "pump_name": pump_name,
                "do_name": do_name,
                "success": False,
            }

    def _repair_multiple_air_pumps_running(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Dict:
        """修复多个气泵同时运行问题"""
        # 获取预期运行的气泵
        active_air_pump_key = f"active_air_pump_{sn}"
        expected_pump = crud.get_kv(db, active_air_pump_key)
        
        repairs_made = []
        running_pumps = issue["pumps_running"]
        
        # 如果没有预期运行的气泵，或者预期的气泵不在运行列表中
        # 则保留第一个运行的气泵，停止其他的
        if not expected_pump or expected_pump not in running_pumps:
            # 选择第一个运行的气泵作为保留的气泵
            keep_pump = running_pumps[0]
            expected_pump = keep_pump
            
            # 更新数据库中的预期状态
            crud.set_kv(db, active_air_pump_key, keep_pump)
            
            # 设置启动时间（如果没有的话）
            air_pump_start_time_key = f"air_pump_start_time_{sn}"
            if not crud.get_kv(db, air_pump_start_time_key):
                current_time = datetime.datetime.now()
                crud.set_kv(db, air_pump_start_time_key, current_time.isoformat())
            
            print(f"[state_monitor] 多个气泵运行但无预期状态，保留 {keep_pump}，停止其他气泵")
        
        # 停止除预期外的所有气泵
        for pump_name in running_pumps:
            if pump_name != expected_pump:
                # 停止非预期的气泵
                do_name = PUMP_TO_DO_MAPPING[pump_name]

                wait_for_device_ready(sn)
                command = create_do_command(do_name=do_name, value=0)
                success, cmd_log_id = send_command_to_device(sn=sn, command=command)

                repairs_made.append(
                    {
                        "pump_name": pump_name,
                        "do_name": do_name,
                        "action": "stop",
                        "success": success,
                    }
                )
                
                print(f"[state_monitor] 停止非预期气泵 {pump_name} (DO: {do_name}), 成功: {success}")

        all_success = all(repair["success"] for repair in repairs_made)
        status = "success" if all_success else "partial_success"
        crud.update_operation_log_status(db, log_id, status)

        return {
            "repair_type": "multiple_air_pumps_fix",
            "expected_pump": expected_pump,
            "kept_pump": expected_pump,
            "repairs_made": repairs_made,
            "success": all_success,
        }

    def _repair_air_pump_runtime_exceeded(
        self, db, sn: str, issue: Dict, log_id: int
    ) -> Optional[Dict]:
        """修复气泵运行时间过长问题"""
        current_air_pump = issue["pump_name"]
        do_name = PUMP_TO_DO_MAPPING.get(current_air_pump)

        if not do_name:
            return None

        # 等待设备准备就绪
        wait_for_device_ready(sn)

        # 发送停止命令
        command = create_do_command(do_name=do_name, value=0)
        success, cmd_log_id = send_command_to_device(sn=sn, command=command)

        if success:
            # 清理数据库状态
            active_air_pump_key = f"active_air_pump_{sn}"
            air_pump_start_time_key = f"air_pump_start_time_{sn}"
            crud.set_kv(db, active_air_pump_key, "")
            crud.set_kv(db, air_pump_start_time_key, "")

            crud.update_operation_log_status(db, log_id, "success")
            return {
                "repair_type": "air_pump_runtime_stop",
                "pump_name": current_air_pump,
                "do_name": do_name,
                "runtime_hours": issue["runtime_hours"],
                "success": True,
            }
        else:
            crud.update_operation_log_status(db, log_id, "failed", "命令发送失败")
            return {
                "repair_type": "air_pump_runtime_stop",
                "pump_name": current_air_pump,
                "do_name": do_name,
                "runtime_hours": issue["runtime_hours"],
                "success": False,
            }


# 全局状态监控实例
_state_monitor = StateMonitor()


def start_state_monitor():
    """启动状态监控"""
    _state_monitor.start()


def stop_state_monitor():
    """停止状态监控"""
    _state_monitor.stop()


def get_state_monitor_status():
    """获取状态监控状态"""
    return {"running": _state_monitor.running, "config": STATE_MONITOR_CONFIG.copy()}


def update_state_monitor_config(new_config: Dict):
    """更新状态监控配置"""
    STATE_MONITOR_CONFIG.update(new_config)
    return STATE_MONITOR_CONFIG.copy()

# /f:/水利站/backend/device_state_recovery.py
"""
设备状态恢复模块
在设备重连时自动检查并恢复设备的运行状态
"""
import datetime
from sqlalchemy.orm import Session
from database import SessionLocal
import crud
from device_control import create_do_command, send_command_to_device
from data_store import DATA_LOCK, DEVICE_DATA


def check_and_recover_device_state(sn: str, device_data: dict) -> bool:
    """
    检查设备重连后的状态，并在必要时恢复运行状态
    
    Args:
        sn: 设备序列号
        device_data: 设备当前上报的数据
        
    Returns:
        bool: 是否执行了状态恢复操作
    """
    db = SessionLocal()
    recovery_performed = False
    
    try:
        current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time_str}] [state_recovery] 开始检查设备 {sn} 的状态恢复需求")
        
        # 检查水泵状态恢复
        water_pump_recovered = _recover_water_pump_state(db, sn, device_data)
        
        # 检查气泵状态恢复
        air_pump_recovered = _recover_air_pump_state(db, sn, device_data)
        
        recovery_performed = water_pump_recovered or air_pump_recovered
        
        if recovery_performed:
            print(f"[{current_time_str}] [state_recovery] 设备 {sn} 状态恢复完成")
        else:
            print(f"[{current_time_str}] [state_recovery] 设备 {sn} 无需状态恢复")
            
    except Exception as e:
        current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time_str}] [state_recovery] 设备 {sn} 状态恢复过程中发生错误: {e}")
        
    finally:
        db.close()
        
    return recovery_performed


def _recover_water_pump_state(db: Session, sn: str, device_data: dict) -> bool:
    """
    恢复水泵运行状态（交替启动逻辑）
    逻辑：
    1. 浮球激活时，检查预期运行的水泵是否实际运行
    2. 浮球复位时，确保所有水泵都停止
    3. 支持交替启动逻辑（每次只运行一个水泵）
    
    Args:
        db: 数据库会话
        sn: 设备序列号
        device_data: 设备当前数据
        
    Returns:
        bool: 是否执行了水泵状态恢复
    """
    current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 获取数据库中记录的预期状态
    active_pump_key = f"active_pump_{sn}"
    start_time_key = f"pump_start_time_{sn}"
    
    expected_active_pump = crud.get_kv(db, active_pump_key)
    start_time_str = crud.get_kv(db, start_time_key)
    
    # 获取设备当前的水泵状态和浮球状态
    water_pump1_status = device_data.get("DO21_status", 0)
    water_pump2_status = device_data.get("DO22_status", 0)
    float1_status = device_data.get("float_switches", {}).get("float1", 0)
    
    recovery_performed = False
    
    # 情况1：浮球激活，但预期运行的水泵未运行
    if float1_status == 1 and expected_active_pump:
        if expected_active_pump == "water_pump1" and water_pump1_status != 1:
            recovery_reason = f"浮球激活但水泵1未运行 (预期: {expected_active_pump})"
            
            # 检查水泵1是否处于自动模式
            pump1_auto_status = device_data.get("water_pump1", {}).get("auto_status", 0)
            if pump1_auto_status == 1:
                # 发送启动指令
                command = create_do_command("DO21", 1)
                success = send_command_to_device(sn, command)
                
                # 记录操作日志
                log = crud.create_operation_log(
                    db=db,
                    operation_type="state_recovery",
                    operation_details=f"设备重连状态恢复: {recovery_reason}",
                    device_sn=sn,
                    command_sent="启动 DO21",
                    execution_status="success" if success else "failed",
                    additional_data={
                        "trigger": "device_reconnection",
                        "float1_status": float1_status,
                        "recovery_command": "DO21=1",
                        "command_success": success
                    }
                )
                
                print(f"[{current_time_str}] [state_recovery] 水泵1状态恢复: {recovery_reason}, 指令发送{'成功' if success else '失败'}")
                recovery_performed = True
        
        elif expected_active_pump == "water_pump2" and water_pump2_status != 1:
            recovery_reason = f"浮球激活但水泵2未运行 (预期: {expected_active_pump})"
            
            # 检查水泵2是否处于自动模式
            pump2_auto_status = device_data.get("water_pump2", {}).get("auto_status", 0)
            if pump2_auto_status == 1:
                # 发送启动指令
                command = create_do_command("DO22", 1)
                success = send_command_to_device(sn, command)
                
                # 记录操作日志
                log = crud.create_operation_log(
                    db=db,
                    operation_type="state_recovery",
                    operation_details=f"设备重连状态恢复: {recovery_reason}",
                    device_sn=sn,
                    command_sent="启动 DO22",
                    execution_status="success" if success else "failed",
                    additional_data={
                        "trigger": "device_reconnection",
                        "float1_status": float1_status,
                        "recovery_command": "DO22=1",
                        "command_success": success
                    }
                )
                
                print(f"[{current_time_str}] [state_recovery] 水泵2状态恢复: {recovery_reason}, 指令发送{'成功' if success else '失败'}")
                recovery_performed = True
    
    # 情况2：浮球未激活，但有水泵在运行
    elif float1_status == 0:
        commands_sent = []
        
        if water_pump1_status == 1:
            command = create_do_command("DO21", 0)
            success = send_command_to_device(sn, command)
            commands_sent.append(f"停止DO21: {'成功' if success else '失败'}")
            
        if water_pump2_status == 1:
            command = create_do_command("DO22", 0)
            success = send_command_to_device(sn, command)
            commands_sent.append(f"停止DO22: {'成功' if success else '失败'}")
        
        if commands_sent:
            # 清理数据库状态
            crud.set_kv(db, active_pump_key, "")
            crud.set_kv(db, start_time_key, "")
            
            # 记录操作日志
            log = crud.create_operation_log(
                db=db,
                operation_type="state_recovery",
                operation_details=f"设备重连状态恢复: 浮球未激活，停止运行的水泵",
                device_sn=sn,
                command_sent="; ".join(commands_sent),
                execution_status="success",
                additional_data={
                    "trigger": "device_reconnection",
                    "float1_status": float1_status,
                    "commands_sent": commands_sent
                }
            )
            
            print(f"[{current_time_str}] [state_recovery] 水泵状态恢复: 浮球未激活，已停止运行的水泵: {'; '.join(commands_sent)}")
            recovery_performed = True
    
    return recovery_performed


def _recover_air_pump_state(db: Session, sn: str, device_data: dict) -> bool:
    """
    恢复气泵运行状态
    
    Args:
        db: 数据库会话
        sn: 设备序列号
        device_data: 设备当前数据
        
    Returns:
        bool: 是否执行了气泵状态恢复
    """
    current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 获取数据库中记录的预期状态
    active_air_pump_key = f"active_air_pump_{sn}"
    air_pump_start_time_key = f"air_pump_start_time_{sn}"
    
    expected_active_air_pump = crud.get_kv(db, active_air_pump_key)
    air_start_time_str = crud.get_kv(db, air_pump_start_time_key)
    
    # 如果数据库中没有活跃气泵记录，无需恢复
    if not expected_active_air_pump or not air_start_time_str:
        return False
        
    # 获取设备当前的气泵状态
    air_pump1_status = device_data.get("air_pump1", {}).get("status", 0)
    air_pump2_status = device_data.get("air_pump2", {}).get("status", 0)
    
    # 检查预期运行的气泵是否实际在运行
    should_recover = False
    recovery_reason = ""
    
    if expected_active_air_pump == "air_pump1" and air_pump1_status != 1:
        should_recover = True
        recovery_reason = f"气泵1应该运行但未运行 (预期: {expected_active_air_pump})"
    elif expected_active_air_pump == "air_pump2" and air_pump2_status != 1:
        should_recover = True
        recovery_reason = f"气泵2应该运行但未运行 (预期: {expected_active_air_pump})"
    
    if should_recover:
        # 检查气泵是否处于自动模式
        pump_auto_status = device_data.get(expected_active_air_pump, {}).get("auto_status", 0)
        if pump_auto_status != 1:
            print(f"[{current_time_str}] [state_recovery] 气泵 {expected_active_air_pump} 未处于自动模式，跳过恢复")
            return False
            
        # 发送启动指令
        do_name = "DO23" if expected_active_air_pump == "air_pump1" else "DO24"
        command = create_do_command(do_name, 1)
        success = send_command_to_device(sn, command)
        
        # 记录操作日志
        log = crud.create_operation_log(
            db=db,
            operation_type="state_recovery",
            operation_details=f"设备重连状态恢复: {recovery_reason}",
            device_sn=sn,
            command_sent=f"启动 {do_name}",
            execution_status="success" if success else "failed",
            additional_data={
                "trigger": "device_reconnection",
                "expected_air_pump": expected_active_air_pump,
                "recovery_command": f"{do_name}=1",
                "command_success": success
            }
        )
        
        print(f"[{current_time_str}] [state_recovery] 气泵状态恢复: {recovery_reason}, 指令发送{'成功' if success else '失败'}")
        return True
        
    return False


def should_trigger_state_recovery(sn: str, device_data: dict) -> bool:
    """
    判断是否应该触发状态恢复逻辑
    
    通过检查设备数据的连续性来判断是否为重连场景
    
    Args:
        sn: 设备序列号
        device_data: 设备当前数据
        
    Returns:
        bool: 是否应该触发状态恢复
    """
    with DATA_LOCK:
        # 如果这是该设备第一次出现在内存中，认为是重连
        if sn not in DEVICE_DATA:
            return True
            
        # 检查上次更新时间，如果间隔超过5分钟，认为是重连
        last_updated_str = DEVICE_DATA[sn].get("last_updated")
        if last_updated_str:
            try:
                last_updated = datetime.datetime.strptime(last_updated_str, "%Y-%m-%d %H:%M:%S")
                time_diff = datetime.datetime.now() - last_updated
                if time_diff.total_seconds() > 300:  # 5分钟
                    return True
            except:
                return True
                
    return False
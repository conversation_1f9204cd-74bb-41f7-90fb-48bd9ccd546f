# CLAUDE.md

始终使用中文与我交流

## 项目概述

这是一个基于Python的工业水泵控制与监控系统，专门设计用于自动化水泵站管理。系统通过TCP服务器接收设备数据，并提供完整的REST API接口用于远程控制和监控。该系统具有智能的双泵协作控制逻辑、实时状态监控、任务调度和完整的历史数据管理功能。

## 快速启动

### 完整系统启动

```bash
python main.py
```

启动内容包括：

- TCP服务器（端口8889）- 接收设备数据
- FastAPI服务器（端口8500）- REST API接口
- 后台任务调度服务
- 智能状态监控系统
- 数据库自动初始化

### 开发模式启动

```bash
uvicorn api_server:app --host 0.0.0.0 --port 8500 --reload
```

### 仅启动TCP服务器

```bash
python generic_tcp_server.py
```

## 系统架构

### 核心组件架构

本系统采用多层架构设计，各组件职责明确：

**main.py** - 系统启动协调器

- 数据库初始化和表结构创建
- TCP服务器后台线程启动
- 系统状态初始化（水泵轮换设置）
- 任务调度服务启动
- FastAPI服务器主线程运行

**generic_tcp_server.py** - TCP设备通信核心

- 监听8889端口处理设备连接
- 多线程客户端连接管理
- 智能水泵控制逻辑执行
- 设备数据实时存储和缓存
- 连接异常处理和设备重连恢复

**api_server.py** - REST API服务层

- 设备数据查询和控制接口
- DO（数字输出）控制命令处理
- CORS跨域支持，便于前端集成
- 实时数据访问通过共享数据存储

**data_store.py** - 线程安全的共享状态管理

- DEVICE_DATA：按设备SN索引的实时数据
- CONNECTED_CLIENTS：活跃TCP连接池管理
- 并发访问保护的线程锁机制

### 数据库层设计

**database.py** - SQLAlchemy数据库配置

- 数据库：sqlite:///./sql_app.db
- 多线程安全的SQLite配置

**models.py** - ORM数据模型

- DeviceDataLog：设备历史数据日志存储
- TaskMetadata：任务调度信息持久化
- SystemKVStore：系统级键值对状态持久化
- OperationLog：详细的系统操作日志

**crud.py** - 数据库操作统一接口

- 支持复杂查询条件（时间范围、字段筛选、关键字搜索）
- 分页查询支持
- 事务安全处理

### 设备控制与调度系统

**device_control.py** - 设备命令生成与传输

- 标准化DO控制命令创建
- TCP连接命令发送
- 命令执行状态跟踪

**scheduler_service.py** - APScheduler任务管理

- 持久化作业存储使用SQLAlchemy
- 支持循环和序列任务类型
- 后台执行，支持时区配置（Asia/Shanghai）

**pump_control_new.py** - 新型智能水泵控制逻辑

- 浮球激活时优先启动水泵1（DO21）
- 超过配置时间后启动水泵2（DO22）辅助
- 浮球复位时同时关闭所有水泵
- 状态持久化，支持系统重启恢复

## 设备通信协议

设备通过TCP连接发送JSON格式数据，包含：

- 设备序列号（SN）标识
- 浮球开关状态（float1, float2）
- 水泵运行状态（water_pump1, water_pump2, air_pump1, air_pump2）
- 电流检测数据（dianliucaiji2）

### 硬件接口映射

**数字输入接口（DI）：**

- DI01：浮球开关状态
- DI11-DI14：旋钮开关停止档位
- DI15-DI18：旋钮开关自动档位（水泵1、水泵2、气泵1、气泵2）
- DI21-DI24：旋钮开关手动档位

**数字输出接口（DO）：**

- DO01：故障指示灯
- DO21：水泵1控制
- DO22：水泵2控制  
- DO23：气泵1控制
- DO24：气泵2控制

## 核心功能特性

### 新型智能水泵和气泵控制逻辑（2025-08-04更新）

系统实现了全新的交替启动和智能故障切换控制策略：

**水泵交替启动逻辑：**
1. **交替启动机制**：浮球激活时，系统交替启动水泵1和水泵2，实现负载均衡
2. **故障自动切换**：当目标水泵故障时，自动切换到可用的水泵
3. **浮球复位响应**：浮球复位时，关闭当前运行的水泵
4. **状态持久化**：下次启动的水泵信息持久化存储，支持系统重启恢复

**气泵智能故障切换逻辑：**
1. **故障检测**：实时检测气泵运行状态和电流异常
2. **自动切换**：当一个气泵故障时，另一个气泵自动持续运行
3. **互补运行**：一个气泵关闭时，另一个气泵自动接管工作
4. **自动模式检查**：只有在设备处于自动档位时才执行自动控制逻辑

**水泵故障检测和备用切换：**
1. **DO状态监控**：检查DO输出与实际水泵运行状态的匹配性
2. **电流异常检测**：监控水泵电流是否在正常范围内
3. **故障自动切换**：检测到故障时自动切换到备用水泵
4. **运行记录跟踪**：详细记录每次切换的原因和结果

### 硬件感知的时序控制

内置继电器切换延时，防止硬件冲突：

- 水泵停止延时：5秒（可配置）
- 设备命令间隔：同一设备命令间隔1秒
- 强制切换延时：2小时超时轮换时8秒延时

### 全面的操作日志系统

所有设备命令和水泵操作都有详细记录：

- 执行状态跟踪（成功/失败/待处理）
- 错误信息详细记录
- 状态验证结果记录
- 精确的时间信息记录

### 智能状态监控系统

自动化看门狗系统持续监控系统健康：

- 每60秒监控水泵状态vs预期状态（可配置）
- 检测浮球-水泵逻辑不一致问题
- 支持合法的双泵运行场景识别
- 监控水泵运行时间限制
- 自动修复检测到的问题

### 线程安全设计

所有共享数据结构使用threading.Lock()保护并发访问安全

### 状态持久化

关键系统状态（如水泵启动时间、运行记录）持久化存储在数据库中，支持系统重启后状态恢复

### 实时+历史数据

实时数据通过共享内存快速访问，历史数据通过SQLite持久化存储

## API接口服务

主要API端点（<http://localhost:8500>）：

### 数据查询接口

- `/data` - 获取当前设备实时数据
- `/data/{device_sn}` - 获取指定设备数据
- `/history/{device_sn}` - 设备历史数据查询
- `/history` - 所有设备历史数据查询

### 设备控制接口

- `/control/{sn}` - 设备DO控制
- `/clients` - 获取已连接设备列表

### 任务调度接口

- `/schedule/task` - 创建延时任务
- `/schedule/tasks` - 查询所有任务
- `/schedule/task/{job_id}` - 取消指定任务
- `/schedule/cycle` - 创建循环任务
- `/schedule/sequence` - 创建顺序循环任务

### 系统监控接口

- `/state-monitor/status` - 状态监控系统状态
- `/state-monitor/config` - 更新监控配置
- `/state-monitor/manual-check` - 手动触发状态检查
- `/state-monitor/logs` - 状态监控日志

### 操作日志接口

- `/operation-logs` - 查询所有操作日志
- `/operation-logs/pump-control` - 查询水泵控制日志

### 配置管理接口

- `/device-timing-config` - 设备延时配置管理
- `/pump-config` - 水泵配置参数管理

### API文档

- `/docs` - Swagger自动生成的API文档

## 状态监控系统详解

系统包含智能状态监控看门狗，预防和修复浮球-水泵逻辑问题：

### 监控能力

- **自动检查**：每60秒执行一次检查（可配置）
- **状态对比**：数据库预期状态vs设备实际状态
- **问题检测**：浮球-水泵匹配、双泵运行、运行时间超限
- **自动修复**：考虑硬件时序的自动问题修复

### 核心配置文件

- `state_monitor.py` - 监控逻辑和修复功能主文件
- `device_state_recovery.py` - 设备重连状态恢复
- `device_timing.py` - 硬件感知的延时控制

### 常见问题检测与修复

1. **水泵未运行**：预期水泵应运行但设备显示关闭
2. **浮球激活无水泵1**：浮球激活但水泵1未运行（应优先启动水泵1）
3. **浮球未激活水泵运行**：浮球未激活但水泵仍在运行（应停止所有水泵）
4. **水泵2无记录**：水泵2运行但无正确的启动时间记录
5. **运行时间超限**：水泵运行时间过长，触发强制轮换

### 配置参数

- `check_interval_seconds`: 60（监控频率）
- `auto_repair_enabled`: true（启用自动修复）
- `max_pump_runtime_hours`: 3（最大水泵运行时间）

## Node-RED工作流集成

### Node-RED工作流实现

系统提供了完整的Node-RED可视化编程工作流（flows.json），实现与Python后端相同的水泵控制逻辑：

**工作流架构：**

```text
定时浮球检查(5秒) → 获取浮球状态(DI01) → 检查自动档位 → 浮球状态处理逻辑 → 动作分发 → 设备控制
                                                     ↓
定时档位读取(10秒) → 读取DI15-DI18 → 存储档位状态到全局变量
                                                     ↓
定时故障灯检查(5秒) → 检查故障灯状态 → 控制故障灯(DO01)
```

**核心特性：**

- 完全兼容pump_control_new.py的三阶段控制逻辑
- 使用实际硬件DI/DO接口编号
- 智能档位检查，只有自动模式才执行控制
- 故障灯自动提示系统状态
- 丰富的调试信息输出
- 支持水泵2延时配置

## 最近更新记录（2025-08-04）

### 水泵和气泵控制逻辑全面重构

**任务目标**：实现水泵交替启动和气泵故障检测切换逻辑

**完成内容**：

#### 1. 水泵交替启动逻辑实现

- **新的控制策略**：
  - 浮球激活时交替启动水泵1和水泵2，避免单泵长期运行
  - 浮球复位时关闭当前运行的水泵
  - 通过数据库持久化下次要使用的水泵信息

- **故障检测和切换**：
  - 检查DO状态与实际运行状态的匹配性
  - 监控电流异常（过低或过高）
  - 自动故障时切换到备用水泵

#### 2. 气泵智能故障切换逻辑

- **故障检测机制**：
  - 检测DO输出与实际运行状态不匹配
  - 监控气泵电流异常
  - 实时故障状态评估

- **自动切换策略**：
  - 当存在气泵故障时，运行没有故障的气泵，且一直运行
  - 一个气泵关闭时，另一个气泵一直运行
  - 只有在自动模式下才执行控制逻辑

#### 3. 状态监控系统升级

- **新的检查逻辑**：
  - 适配交替启动的水泵检查
  - 新增气泵故障检测和切换检查
  - 移除旧的双泵运行异常检测（现在合法）

- **自动修复功能**：
  - `_repair_float_active_no_pump`: 浮球激活时启动下一个要使用的水泵
  - `_repair_air_pump_failover`: 气泵故障时切换到可用气泵
  - 支持水泵和气泵的智能故障恢复

#### 4. 故障检测函数

- **is_pump_faulty()函数**：
  - 检查DO状态与实际运行状态匹配性
  - 电流异常检测（过低或过高阈值）
  - 支持水泵和气泵的统一故障检测

**技术亮点**：

- 负载均衡：水泵交替启动，延长设备寿命
- 智能故障切换：自动检测并切换到可用设备
- 状态持久化：系统重启后恢复交替状态
- 全面监控：实时检测设备运行状态和故障
- 自动修复：状态监控系统自动修复检测到的问题

**文件变更**：

- `pump_control_new.py`: 完全重构水泵和气泵控制逻辑
- `state_monitor.py`: 更新状态检查和修复逻辑以支持新控制方式
- `CLAUDE.md`: 更新文档反映新的控制逻辑

## 最近更新记录（2025-07-24）

### Node-RED工作流实现水泵控制逻辑

**任务目标**：实现pump_control_new.py中的水泵控制逻辑到Node-RED工作流，并适配实际硬件接口

**完成内容**：

#### 1. Node-RED工作流基础实现

- 创建完整的flows.json文件，实现pump_control_new.py的三阶段逻辑：
  - 浮球复位(1→0)：关闭所有水泵
  - 浮球首次激活(0→1)：启动水泵1
  - 浮球持续激活(1→1)：检查是否需要启动水泵2
- 使用flow context存储状态信息（前次浮球状态、水泵启动时间等）
- 实现可配置的水泵2启动延时（默认2小时）

#### 2. 硬件接口适配

根据实际硬件接口定义进行完整适配：

- **输入接口**：
  - DI01：浮球开关状态
  - DI15-DI18：设备自动档位检测（水泵1、水泵2、气泵1、气泵2）
- **输出接口**：
  - DO01：故障灯控制
  - DO21：水泵1控制
  - DO22：水泵2控制
  - DO23：气泵1控制（预留）
  - DO24：气泵2控制（预留）

#### 3. 智能档位检查系统

- 每10秒读取DI15-DI18档位状态并存储到全局变量
- 只有在设备处于自动档位时才执行自动控制逻辑
- 非自动模式时自动点亮故障灯(DO01)提示
- 正常自动模式时关闭故障灯

#### 4. 故障灯控制逻辑

- 每5秒检查系统状态并更新故障灯
- 故障灯状态基于设备档位：
  - 自动模式：故障灯熄灭
  - 非自动模式：故障灯点亮

#### 5. 增强的调试和监控功能

- **详细的控制台日志**：
  - 浮球状态变化追踪
  - 档位检查信息
  - 水泵2启动条件计算过程
  - 双泵运行状态监控
- **多层调试节点**：
  - 状态详情输出：完整消息对象
  - 动作日志：具体操作描述
  - 操作结果：设备控制执行结果
- **所有调试信息同时输出到侧边栏和控制台**

**技术亮点**：

- 使用edge节点直接与M300设备通信
- 全局变量管理档位状态
- 详细的时间计算和状态跟踪
- 多重安全检查确保只有在合适条件下才执行控制

**文件变更**：

- `flows.json`: 包含31个节点的完整Node-RED工作流定义文件

### 水泵控制逻辑重构（早期会话）

**任务目标**：按照pump_control_new中的新的水泵控制逻辑调整state_monitor中对水泵的状态监测逻辑

**完成内容**：

1. **新控制逻辑分析**：理解pump_control_new.py中的优先启动水泵1策略
2. **状态监控适配**：更新state_monitor.py以符合新的控制逻辑：
   - 浮球激活时检查水泵1是否运行（应该首先启动）
   - 浮球未激活时检查所有水泵是否停止（应该同时关闭）
   - 添加对pump2_start_time状态的检查和管理
3. **多泵运行逻辑调整**：移除旧的多水泵运行检查，允许合法的双泵运行场景
4. **修复逻辑重构**：
   - `_repair_float_active_no_pump1`: 启动水泵1当浮球激活但水泵1未运行
   - `_repair_pump2_without_record`: 停止无记录的水泵2
   - `_repair_float_inactive_pump_running`: 浮球复位时同时关闭所有水泵
5. **遗留代码清理**：移除data_store.py中不再使用的变量：
   - `NEXT_PUMP_TO_USE` - 不再需要轮换逻辑
   - `FLOAT1_ACTIVE_PUMP` - 不再需要记录激活泵
   - `PUMP_LOGIC_LOCK` - 不再需要专门锁

**关键变更**：

- 状态监控完全兼容新的水泵优先级控制逻辑
- 删除了旧的水泵轮换机制相关代码
- 支持双泵运行场景而不报告为异常
- 确保所有控制操作都基于水泵的auto_status状态

**文件变更**：

- `state_monitor.py`: 状态检查和修复逻辑更新
- `data_store.py`: 清理遗留的轮换逻辑变量
- `generic_tcp_server.py`: 移除不再使用的导入

## 开发说明

### 技术要求

- Python 3.11+ 必需
- 无外部依赖管理文件（requirements.txt, pyproject.toml）
- 使用标准库和常用包（FastAPI, SQLAlchemy, APScheduler）
- 所有注释和文档使用中文
- SQLite数据库文件：sql_app.db（自动创建）
- 状态监控随main.py自动启动

### 部署建议

1. **生产环境**：使用main.py启动完整系统
2. **开发环境**：使用uvicorn启动API服务器，支持热重载
3. **测试环境**：可单独启动TCP服务器进行设备通信测试

### 监控和维护

- 系统自动生成详细的操作日志
- 支持通过API查询系统运行状态
- 数据库自动备份（SQLite文件备份）
- 支持远程配置修改

### 扩展性

- 模块化设计便于功能扩展
- 标准化的API接口便于第三方集成
- 可配置的参数支持不同应用场景
- 插件式的任务调度系统

本系统特别适用于工业水泵站、污水处理厂、农业灌溉系统等需要可靠自动化控制的工业场景。

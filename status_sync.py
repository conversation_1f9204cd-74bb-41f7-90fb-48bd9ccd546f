# /f:/水利站/backend/status_sync.py
import datetime
from database import SessionLocal
import crud
from data_store import DEVICE_DATA, DATA_LOCK
from device_control import create_do_command, send_command_to_device
from status_verification import PUMP_TO_DO_STATUS_MAPPING

PUMP_TO_DO_MAPPING = {
    "water_pump1": "DO21",
    "water_pump2": "DO22",
}

def get_active_pump_key(sn: str) -> str:
    return f"active_pump_{sn}"

def check_and_fix_pump_status_mismatch(sn: str):
    """
    检查并修复水泵状态不一致的问题。
    比较数据库记录的期望状态与设备实际状态，如果不一致则尝试修复。
    """
    current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    db = SessionLocal()
    try:
        # 获取数据库中记录的活跃水泵
        active_pump_key = get_active_pump_key(sn)
        expected_active_pump = crud.get_kv(db, active_pump_key)
        
        if not expected_active_pump:
            print(f"[{current_time_str}] [status_sync] 设备 {sn} 没有记录的活跃水泵")
            return
            
        # 获取设备当前实际状态
        with DATA_LOCK:
            device_data = DEVICE_DATA.get(sn, {})
            
        # 检查期望的水泵状态
        expected_do_key = PUMP_TO_DO_MAPPING.get(expected_active_pump)
        expected_status_key = PUMP_TO_DO_STATUS_MAPPING.get(expected_active_pump)
        
        if not expected_do_key or not expected_status_key:
            print(f"[{current_time_str}] [status_sync] 无效的水泵名称: {expected_active_pump}")
            return
            
        actual_status = device_data.get(expected_status_key)
        
        if actual_status != 1:
            # 发现状态不一致
            print(f"[{current_time_str}] [status_sync] 发现状态不一致!")
            print(f"  - 期望运行: {expected_active_pump} (DO: {expected_do_key})")
            print(f"  - 实际状态: {actual_status}")
            
            # 记录不一致日志
            crud.create_operation_log(
                db=db,
                operation_type="status_mismatch",
                operation_details=f"发现状态不一致 - 期望 {expected_active_pump} 运行，实际状态为 {actual_status}",
                device_sn=sn,
                execution_status="pending",
                additional_data={
                    "expected_pump": expected_active_pump,
                    "expected_do": expected_do_key,
                    "actual_status": actual_status,
                    "float1_status": device_data.get("float_switches", {}).get("float1")
                }
            )
            
            # 检查浮球状态决定是否需要修复
            float1_status = device_data.get("float_switches", {}).get("float1")
            if float1_status == 1:
                # 浮球激活，应该有水泵运行，尝试重新启动
                print(f"[{current_time_str}] [status_sync] 浮球激活，尝试重新启动 {expected_active_pump}")
                command = create_do_command(do_name=expected_do_key, value=1)
                success, cmd_log_id = send_command_to_device(sn=sn, command=command)
                
                if success:
                    print(f"[{current_time_str}] [status_sync] 重新启动命令发送成功")
                else:
                    print(f"[{current_time_str}] [status_sync] 重新启动命令发送失败")
                    
            elif float1_status == 0:
                # 浮球未激活，应该清除数据库中的活跃状态
                print(f"[{current_time_str}] [status_sync] 浮球未激活，清除活跃水泵记录")
                crud.set_kv(db, active_pump_key, "")
                crud.set_kv(db, f"pump_start_time_{sn}", "")
                
        else:
            print(f"[{current_time_str}] [status_sync] 设备 {sn} 状态一致: {expected_active_pump} 正在运行")
            
    except Exception as e:
        print(f"[{current_time_str}] [status_sync] 检查状态时发生错误: {e}")
    finally:
        db.close()


def run_status_sync_check():
    """
    运行状态同步检查，检查所有连接的设备
    """
    from data_store import CONNECTED_CLIENTS, CLIENTS_LOCK
    
    current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time_str}] [status_sync] 开始状态同步检查...")
    
    with CLIENTS_LOCK:
        connected_devices = list(CONNECTED_CLIENTS.keys())
    
    for sn in connected_devices:
        check_and_fix_pump_status_mismatch(sn)
    
    print(f"[{current_time_str}] [status_sync] 状态同步检查完成")


if __name__ == "__main__":
    # 可以直接运行此脚本进行状态检查
    run_status_sync_check()
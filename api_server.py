# /f:/水利站/backend/api_server.py
from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import copy
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from typing import List, Any, Optional
import datetime
import json
import os
from pathlib import Path

# 导入共享数据存储和线程锁
from data_store import DEVICE_DATA, DATA_LOCK, CONNECTED_CLIENTS, CLIENTS_LOCK
import data_store

# 导入数据库、模型和操作函数
import crud
import models
from database import SessionLocal

# 导入设备控制模块和调度服务
import device_control
import scheduler_service

# 导入用电度数统计模块
import power_consumption

# 导入设备状态恢复功能 
try:
    from device_state_recovery import check_and_recover_device_state
except ImportError:
    def check_and_recover_device_state(sn, device_data):
        return False

# 导入设备控制功能
try:
    from device_control import create_do_command, send_command_to_device
except ImportError:
    def create_do_command(do_name, value):
        return f"{do_name}={value}"
    def send_command_to_device(sn, command):
        return False, None

# 导入设备等待功能
try:
    from device_timing import wait_for_device_ready
except ImportError:
    def wait_for_device_ready(sn):
        import time
        time.sleep(0.1)


# -- 新增设备映射 --
# 建立DO名称与设备数据中键名的映射关系
DO_TO_DEVICE_MAPPING = {
    "DO21": "water_pump1",
    "DO22": "water_pump2",
    "DO23": "air_pump1",
    "DO24": "air_pump2",
}
# -- 映射结束 --

# 创建FastAPI应用实例
app = FastAPI(
    title="设备数据API",
    description="用于查询通过TCP服务器收集的最新设备数据和历史数据的API。新增了DO控制和任务调度功能。",
    version="1.2.0",
)

# -- 新增CORS跨域配置 --
# 定义允许的源列表
origins = [
    "http://jscn.sz-hgy.com",  # 您部署的生产环境前端地址
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # 允许访问的源
    allow_credentials=True,  # 支持 cookie
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有请求头
)
# -- CORS配置结束 --


# -- 新增开始 --
def _check_auto_status(sn: str, do_name: str):
    """
    检查指定设备上的DO对应模块是否处于自动模式。
    如果不是，则会引发HTTPException。
    """
    # 禁止对M300设备进行任务调度（M300本地自主控制）
    if sn.startswith("02800125"):
        raise HTTPException(
            status_code=403,
            detail=f"M300设备 '{sn}' 采用本地自主控制，不支持远程任务调度。",
        )
    
    device_key = DO_TO_DEVICE_MAPPING.get(do_name)
    if not device_key:
        raise HTTPException(
            status_code=400,
            detail=f"无效的DO名称: '{do_name}'。无法找到对应的设备模块。",
        )

    with DATA_LOCK:
        device_data = copy.deepcopy(DEVICE_DATA.get(sn))

    if not device_data:
        raise HTTPException(
            status_code=404, detail=f"设备SN '{sn}' 未连接或尚未上报数据。"
        )

    module_status = device_data.get(device_key)
    if not module_status or module_status.get("auto_status") != 1:
        raise HTTPException(
            status_code=403,  # 403 Forbidden
            detail=f"无法安排任务：设备模块 '{device_key}' (SN: {sn}) 未处于自动模式(auto_status: 1)。",
        )


# 定义API请求体的数据模型
class DOControlRequest(BaseModel):
    do_name: str = Field(..., description="要控制的DO名称, 例如 'DO21', 'DO22' 等。")
    value: int = Field(
        ..., ge=0, le=1, description="目标状态, 0 表示断开, 1 表示闭合。"
    )


class ScheduleRequest(BaseModel):
    sn: str = Field(..., description="设备序列号。")
    do_name: str = Field(..., description="要控制的DO名称。")
    value: int = Field(
        ..., ge=0, le=1, description="目标状态, 0 表示断开, 1 表示闭合。"
    )
    delay_minutes: int = Field(..., gt=0, description="从现在开始延迟多少分钟后执行。")


class CycleScheduleRequest(BaseModel):
    sn: str = Field(..., description="设备序列号。")
    do_name: str = Field(..., description="要控制的DO名称。")
    on_minutes: int = Field(..., gt=0, description="每次开启的持续分钟数。")
    off_minutes: int = Field(..., gt=0, description="每次关闭的持续分钟数。")


class SequenceScheduleRequest(BaseModel):
    sn: str = Field(..., description="设备序列号。")
    do_a_name: str = Field(..., description="第一个DO的名称 (e.g., 'DO23')。")
    do_a_minutes: int = Field(..., gt=0, description="第一个DO的开启时长（分钟）。")
    do_b_name: str = Field(..., description="第二个DO的名称 (e.g., 'DO24')。")
    do_b_minutes: int = Field(..., gt=0, description="第二个DO的开启时长（分钟）。")


# -- 新增: 定义历史数据返回模型 --
class HistoryLog(BaseModel):
    id: int
    device_sn: str
    timestamp: datetime.datetime
    raw_data: Any

    class Config:
        from_attributes = True


class HistoryView(BaseModel):
    total: int
    items: List[HistoryLog]


# FastAPI 依赖项，用于获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@app.get("/data", tags=["实时数据"], summary="获取所有设备实时数据")
async def get_all_device_data():
    """
    获取所有已连接设备上报的最新数据。

    返回一个字典，键是设备SN，值是该设备的完整数据。
    """
    with DATA_LOCK:
        # 创建数据的深拷贝以避免在FastAPI处理响应时持有锁
        # 这是一种良好的并发实践
        data_copy = copy.deepcopy(DEVICE_DATA)

    if not data_copy:
        return JSONResponse(
            content={"message": "目前没有收到任何设备数据。"}, status_code=404
        )

    return data_copy


@app.get("/data/{sn}", tags=["实时数据"], summary="根据SN获取指定设备实时数据")
async def get_device_data_by_sn(sn: str):
    """
    根据设备SN（序列号）获取特定设备的最新数据。

    - **sn**: 要查询的设备的序列号 (例如: "02801925060700002997")
    """
    with DATA_LOCK:
        # 创建数据的深拷贝
        device_data = copy.deepcopy(DEVICE_DATA.get(sn))

    if device_data is None:
        # 如果在字典中找不到对应的SN，则返回404错误
        raise HTTPException(
            status_code=404,
            detail=f"未找到SN为 '{sn}' 的设备数据。请检查SN是否正确或设备是否已上报数据。",
        )

    return device_data


@app.get("/history/{sn}", tags=["历史数据"], response_model=HistoryView, summary="根据SN查询设备历史数据")
async def get_device_history(
    sn: str, 
    skip: int = 0, 
    limit: int = 100, 
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    float1: Optional[int] = None,
    float2: Optional[int] = None,
    water_pump1_status: Optional[int] = None,
    water_pump2_status: Optional[int] = None,
    air_pump1_status: Optional[int] = None,
    air_pump2_status: Optional[int] = None,
    search_keyword: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    根据设备SN查询历史数据记录，结果按时间倒序排列，支持分页和高级筛选。
    返回总记录数和当前页的数据列表。

    - **sn**: 要查询的设备的序列号。
    - **skip**: (可选) 跳过的记录数，用于分页，默认为0。
    - **limit**: (可选) 返回的最大记录数，用于分页，默认为100。
    - **start_time**: (可选) 开始时间，ISO格式，如 "2023-12-01T00:00:00Z"
    - **end_time**: (可选) 结束时间，ISO格式，如 "2023-12-31T23:59:59Z"
    - **float1**: (可选) 筛选float1状态 (0或1)
    - **float2**: (可选) 筛选float2状态 (0或1)
    - **water_pump1_status**: (可选) 筛选water_pump1状态 (0或1)
    - **water_pump2_status**: (可选) 筛选water_pump2状态 (0或1)
    - **air_pump1_status**: (可选) 筛选air_pump1状态 (0或1)
    - **air_pump2_status**: (可选) 筛选air_pump2状态 (0或1)
    - **search_keyword**: (可选) 关键字搜索，在原始数据中搜索
    """
    # 构建字段筛选条件
    field_filters = {}
    if float1 is not None:
        field_filters["float1"] = float1
    if float2 is not None:
        field_filters["float2"] = float2
    if water_pump1_status is not None:
        field_filters["water_pump1.status"] = water_pump1_status
    if water_pump2_status is not None:
        field_filters["water_pump2.status"] = water_pump2_status
    if air_pump1_status is not None:
        field_filters["air_pump1.status"] = air_pump1_status
    if air_pump2_status is not None:
        field_filters["air_pump2.status"] = air_pump2_status
    
    history_data = crud.get_device_data_logs_by_sn(
        db, 
        sn=sn, 
        skip=skip, 
        limit=limit,
        start_time=start_time,
        end_time=end_time,
        field_filters=field_filters or None,
        search_keyword=search_keyword
    )
    
    if history_data["total"] == 0:
        raise HTTPException(
            status_code=404, detail=f"未找到SN为 '{sn}' 的设备历史数据。"
        )
    return history_data


@app.get("/history", tags=["历史数据"], response_model=HistoryView, summary="查询所有设备历史数据")
async def get_all_device_history(
    skip: int = 0, 
    limit: int = 100, 
    device_sn: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    float1: Optional[int] = None,
    float2: Optional[int] = None,
    water_pump1_status: Optional[int] = None,
    water_pump2_status: Optional[int] = None,
    air_pump1_status: Optional[int] = None,
    air_pump2_status: Optional[int] = None,
    search_keyword: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    查询所有设备的历史数据记录，结果按时间倒序排列，支持分页和高级筛选。
    
    - **skip**: (可选) 跳过的记录数，用于分页，默认为0。
    - **limit**: (可选) 返回的最大记录数，用于分页，默认为100。
    - **device_sn**: (可选) 筛选特定设备序列号
    - **start_time**: (可选) 开始时间，ISO格式，如 "2023-12-01T00:00:00Z"
    - **end_time**: (可选) 结束时间，ISO格式，如 "2023-12-31T23:59:59Z"
    - **float1**: (可选) 筛选float1状态 (0或1)
    - **float2**: (可选) 筛选float2状态 (0或1)
    - **water_pump1_status**: (可选) 筛选water_pump1状态 (0或1)
    - **water_pump2_status**: (可选) 筛选water_pump2状态 (0或1)
    - **air_pump1_status**: (可选) 筛选air_pump1状态 (0或1)
    - **air_pump2_status**: (可选) 筛选air_pump2状态 (0或1)
    - **search_keyword**: (可选) 关键字搜索，在原始数据中搜索
    """
    # 构建字段筛选条件
    field_filters = {}
    if float1 is not None:
        field_filters["float1"] = float1
    if float2 is not None:
        field_filters["float2"] = float2
    if water_pump1_status is not None:
        field_filters["water_pump1.status"] = water_pump1_status
    if water_pump2_status is not None:
        field_filters["water_pump2.status"] = water_pump2_status
    if air_pump1_status is not None:
        field_filters["air_pump1.status"] = air_pump1_status
    if air_pump2_status is not None:
        field_filters["air_pump2.status"] = air_pump2_status
    
    history_data = crud.get_device_data_logs_by_sn(
        db, 
        sn=device_sn, 
        skip=skip, 
        limit=limit,
        start_time=start_time,
        end_time=end_time,
        field_filters=field_filters or None,
        search_keyword=search_keyword
    )
    
    return history_data


# -- 新增API接口 --


@app.post("/control/{sn}", tags=["设备控制"], summary="立即控制设备DO输出")
async def control_do(sn: str, request: DOControlRequest):
    """
    立即控制指定设备的单个DO（数字输出）。

    - **sn**: 目标设备的序列号。
    - **request body**:
        - **do_name**: 要控制的DO名称 (e.g., "DO21", "DO22", "DO23", "DO24").
        - **value**: 目标状态 (0: 断开, 1: 闭合).
    """
    # 禁止对M300设备进行远程控制（M300本地自主控制）
    if sn.startswith("02800125"):
        raise HTTPException(
            status_code=403,
            detail=f"M300设备 '{sn}' 采用本地自主控制，不支持远程DO控制。",
        )
    
    command = device_control.create_do_command(
        do_name=request.do_name, value=request.value
    )
    success = device_control.send_command_to_device(sn=sn, command=command)

    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"指令发送失败。请确认设备 '{sn}' 是否已连接到服务器。",
        )
    return {"message": f"指令已成功发送到设备 {sn}。"}


@app.get("/clients", tags=["设备控制"], summary="获取已连接设备列表")
async def get_connected_clients():
    """
    获取当前所有已连接到TCP服务器的设备列表。
    """
    with CLIENTS_LOCK:
        # 只返回地址信息，不暴露socket对象
        clients_info = {sn: info["address"] for sn, info in CONNECTED_CLIENTS.items()}
    return clients_info


@app.post("/schedule/task", tags=["任务调度"], status_code=201, summary="安排延时DO控制任务")
async def schedule_do_task(request: ScheduleRequest):
    """
    安排一个未来的DO控制任务。

    - **request body**:
        - **sn**: 设备序列号。
        - **do_name**: DO名称。
        - **value**: 目标状态 (0: 断开, 1: 闭合).
        - **delay_minutes**: 延迟多少分钟后执行。
    """
    # 前置检查：确认设备处于自动模式
    _check_auto_status(sn=request.sn, do_name=request.do_name)

    job_id = scheduler_service.schedule_task(
        sn=request.sn,
        do_name=request.do_name,
        value=request.value,
        delay_minutes=request.delay_minutes,
    )
    return {
        "message": "任务已成功安排。",
        "job_id": job_id,
        "details": scheduler_service.get_scheduled_tasks().get(job_id),
    }


@app.get("/schedule/tasks", tags=["任务调度"], summary="获取已安排的任务列表")
async def get_all_scheduled_tasks():
    """
    获取所有当前已安排但尚未执行的任务列表。
    
    返回数据包括：
    - 任务基本信息（ID、设备SN、DO名称、目标值）
    - 执行时间和剩余时间
    - 任务状态
    - 创建时间
    """
    try:
        current_time = datetime.datetime.now()
        tasks = scheduler_service.get_scheduled_tasks()
        
        # 增强任务信息
        enhanced_tasks = {}
        for job_id, task_info in tasks.items():
            try:
                # 计算剩余时间
                execute_at_str = task_info.get("execute_at", "")
                remaining_minutes = 0
                if execute_at_str:
                    execute_at = datetime.datetime.strptime(execute_at_str, "%Y-%m-%d %H:%M:%S")
                    remaining_delta = execute_at - current_time
                    remaining_minutes = max(0, int(remaining_delta.total_seconds() / 60))
                
                # 获取任务在调度器中的状态
                scheduler_job = None
                try:
                    scheduler_job = scheduler_service.scheduler.get_job(job_id)
                except:
                    pass
                
                enhanced_tasks[job_id] = {
                    **task_info,  # 原有数据
                    "remaining_minutes": remaining_minutes,
                    "created_at": task_info.get("scheduled_at", ""),
                    "run_date": execute_at_str,
                    "scheduler_status": "active" if scheduler_job else "missing",
                    "next_run_time": execute_at_str
                }
            except Exception:
                # 如果处理失败，返回原始数据
                enhanced_tasks[job_id] = task_info
        
        return enhanced_tasks
        
    except Exception as e:
        # 如果增强处理失败，返回原始数据
        return scheduler_service.get_scheduled_tasks()


@app.delete("/schedule/task/{job_id}", tags=["任务调度"], summary="取消指定任务")
async def cancel_scheduled_task(job_id: str):
    """
    根据任务ID取消一个已安排的任务。

    - **job_id**: 要取消的任务ID。
    """
    success = scheduler_service.cancel_task(job_id)
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"未找到ID为 '{job_id}' 的任务，或任务取消失败。",
        )
    return {"message": f"任务 {job_id} 已成功取消。"}


# -- 新增循环任务接口 --


@app.post("/schedule/cycle", tags=["任务调度"], status_code=201, summary="安排循环任务")
async def schedule_do_cycle(
    request: CycleScheduleRequest, db: Session = Depends(get_db)
):
    """
    安排一个周期性循环的DO控制任务。

    任务会立即启动（开启DO），然后按照 "开启时长" -> "关闭时长" 的顺序无限循环。
    例如: on_minutes=240, off_minutes=120 表示 "开启4小时，然后关闭2小时"，并不断重复此循环。

    - **request body**:
        - **sn**: 设备序列号。
        - **do_name**: DO名称。
        - **on_minutes**: 开启时长（分钟）。
        - **off_minutes**: 关闭时长（分钟）。
    """
    # 前置检查：确认设备处于自动模式
    _check_auto_status(sn=request.sn, do_name=request.do_name)

    cycle_id = scheduler_service.schedule_cyclic_task(
        db=db,
        sn=request.sn,
        do_name=request.do_name,
        on_minutes=request.on_minutes,
        off_minutes=request.off_minutes,
    )
    return {
        "message": "循环任务已成功安排。",
        "cycle_id": cycle_id,
        "details": scheduler_service.get_cyclic_tasks().get(cycle_id),
    }


@app.get("/schedule/cycles", tags=["任务调度"], summary="获取循环任务列表")
async def get_all_cyclic_tasks():
    """
    获取所有当前活动的循环任务列表。
    
    返回数据包括：
    - 循环任务基本信息
    - 当前阶段和下次运行时间
    - 作业健康状态
    - 创建时间
    """
    try:
        current_time = datetime.datetime.now()
        tasks = scheduler_service.get_cyclic_tasks()
        
        # 增强循环任务信息
        enhanced_tasks = {}
        for cycle_id, cycle_info in tasks.items():
            try:
                # 获取作业状态
                on_job_id = cycle_info.get("on_job_id")
                off_job_id = cycle_info.get("off_job_id")
                
                job_status = {
                    "on_job_active": False,
                    "off_job_active": False,
                    "next_run_time": None,
                    "current_phase": "unknown"
                }
                
                if on_job_id and off_job_id:
                    try:
                        on_job = scheduler_service.scheduler.get_job(on_job_id)
                        off_job = scheduler_service.scheduler.get_job(off_job_id)
                        
                        job_status["on_job_active"] = on_job is not None
                        job_status["off_job_active"] = off_job is not None
                        
                        if on_job and off_job:
                            on_next = on_job.next_run_time
                            off_next = off_job.next_run_time
                            
                            if on_next and off_next:
                                # 找到最近的下次运行时间
                                next_run = min(on_next, off_next)
                                job_status["next_run_time"] = next_run.strftime("%Y-%m-%d %H:%M:%S")
                                
                                # 判断当前阶段
                                if next_run == on_next:
                                    job_status["current_phase"] = "off"  # 即将开启
                                else:
                                    job_status["current_phase"] = "on"   # 即将关闭
                    except Exception:
                        pass
                
                enhanced_tasks[cycle_id] = {
                    **cycle_info,  # 原有数据
                    "created_at": cycle_info.get("scheduled_at", ""),
                    "next_run_time": job_status["next_run_time"],
                    "current_phase": job_status["current_phase"],
                    "job_health": {
                        "on_job_active": job_status["on_job_active"],
                        "off_job_active": job_status["off_job_active"],
                        "healthy": job_status["on_job_active"] and job_status["off_job_active"]
                    }
                }
            except Exception:
                # 如果处理失败，返回原始数据
                enhanced_tasks[cycle_id] = cycle_info
        
        return enhanced_tasks
        
    except Exception as e:
        # 如果增强处理失败，返回原始数据
        return scheduler_service.get_cyclic_tasks()


@app.delete("/schedule/cycle/{cycle_id}", tags=["任务调度"], summary="取消循环任务")
async def cancel_cyclic_task(cycle_id: str, db: Session = Depends(get_db)):
    """
    根据ID取消一个正在运行的循环任务。

    取消后会立即发送一个关闭指令，以确保设备处于安全状态。

    - **cycle_id**: 要取消的循环任务ID。
    """
    success = scheduler_service.cancel_cyclic_task(db=db, cycle_id=cycle_id)
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"未找到ID为 '{cycle_id}' 的循环任务，或任务取消失败。",
        )
    return {"message": f"循环任务 {cycle_id} 已成功取消。"}


# -- 循环任务接口结束 --


# -- 新增顺序循环任务接口 --


@app.post("/schedule/sequence", tags=["任务调度"], status_code=201, summary="安排顺序循环任务")
async def schedule_sequence_task(
    request: SequenceScheduleRequest, db: Session = Depends(get_db)
):
    """
    安排一个顺序循环任务 (A运行 -> B运行 -> A运行 ...)。

    任务会立即启动设备A，运行指定分钟后关闭A并同时启动B，
    B运行指定分钟后关闭，然后整个循环重新开始。
    """
    # 前置检查：确认两个设备都处于自动模式
    _check_auto_status(sn=request.sn, do_name=request.do_a_name)
    _check_auto_status(sn=request.sn, do_name=request.do_b_name)

    sequence_id = scheduler_service.schedule_sequence_task(
        db=db,
        sn=request.sn,
        do_a_name=request.do_a_name,
        do_a_minutes=request.do_a_minutes,
        do_b_name=request.do_b_name,
        do_b_minutes=request.do_b_minutes,
    )
    return {
        "message": "顺序循环任务已成功安排。",
        "sequence_id": sequence_id,
        "details": scheduler_service.get_sequence_tasks().get(sequence_id),
    }


@app.get("/schedule/sequences", tags=["任务调度"], summary="获取顺序循环任务列表")
async def get_all_sequence_tasks():
    """
    获取所有当前活动的顺序循环任务列表。
    
    返回数据包括：
    - 顺序任务基本信息
    - 健康状态检查
    - 下次运行时间
    - 创建时间
    """
    try:
        current_time = datetime.datetime.now()
        tasks = scheduler_service.get_sequence_tasks()
        
        # 增强顺序任务信息
        enhanced_tasks = {}
        for sequence_id, sequence_info in tasks.items():
            try:
                # 执行健康检查
                health_check = scheduler_service.check_sequence_task_health(sequence_id)
                
                # 获取下次运行时间
                next_run_time = None
                job_ids = sequence_info.get("job_ids", [])
                
                if job_ids and health_check["healthy"]:
                    try:
                        next_runs = []
                        for job_id in job_ids:
                            job = scheduler_service.scheduler.get_job(job_id)
                            if job and job.next_run_time:
                                next_runs.append(job.next_run_time)
                        
                        if next_runs:
                            next_run = min(next_runs)
                            next_run_time = next_run.strftime("%Y-%m-%d %H:%M:%S")
                    except Exception:
                        pass
                
                enhanced_tasks[sequence_id] = {
                    **sequence_info,  # 原有数据
                    "created_at": sequence_info.get("scheduled_at", ""),
                    "next_run_time": next_run_time,
                    "health_status": {
                        "healthy": health_check["healthy"],
                        "reason": health_check["reason"],
                        "missing_jobs": health_check.get("missing_jobs", []),
                        "total_jobs": health_check.get("total_jobs", 0),
                        "active_jobs": health_check.get("total_jobs", 0) - len(health_check.get("missing_jobs", []))
                    }
                }
            except Exception:
                # 如果处理失败，返回原始数据
                enhanced_tasks[sequence_id] = sequence_info
        
        return enhanced_tasks
        
    except Exception as e:
        # 如果增强处理失败，返回原始数据
        return scheduler_service.get_sequence_tasks()


@app.delete("/schedule/sequence/{sequence_id}", tags=["任务调度"], summary="取消顺序循环任务")
async def cancel_sequence_task(sequence_id: str, db: Session = Depends(get_db)):
    """
    根据ID取消一个正在运行的顺序循环任务。

    取消后会立即发送关闭指令给涉及的两个DO。
    """
    success = scheduler_service.cancel_sequence_task(db=db, sequence_id=sequence_id)
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"未找到ID为 '{sequence_id}' 的顺序任务，或任务取消失败。",
        )
    return {"message": f"顺序循环任务 {sequence_id} 已成功取消。"}


# -- 顺序循环任务接口结束 --


# -- 任务状态汇总接口 --

@app.get("/schedule/tasks/status", tags=["任务调度"], summary="获取任务状态汇总")
async def get_tasks_status_summary():
    """
    获取所有任务类型的状态摘要和剩余时间。
    
    返回信息包括：
    - 单次任务列表（包含剩余时间计算）
    - 循环任务列表（包含当前阶段和状态）
    - 顺序任务列表（包含当前设备和状态）
    """
    try:
        current_time = datetime.datetime.now()
        
        # 获取单次任务
        single_tasks = []
        scheduled_tasks = scheduler_service.get_scheduled_tasks()
        for job_id, task_info in scheduled_tasks.items():
            try:
                # 计算剩余时间
                execute_at_str = task_info.get("execute_at", "")
                if execute_at_str:
                    execute_at = datetime.datetime.strptime(execute_at_str, "%Y-%m-%d %H:%M:%S")
                    remaining_delta = execute_at - current_time
                    remaining_minutes = max(0, int(remaining_delta.total_seconds() / 60))
                else:
                    remaining_minutes = 0
                
                single_tasks.append({
                    "job_id": job_id,
                    "do_name": task_info.get("do_name", ""),
                    "value": task_info.get("value", 0),
                    "remaining_minutes": remaining_minutes,
                    "status": task_info.get("status", "pending"),
                    "execute_at": execute_at_str,
                    "device_sn": task_info.get("sn", "")
                })
            except Exception as e:
                # 跳过解析错误的任务
                continue
        
        # 获取循环任务
        cyclic_tasks = []
        cyclic_task_list = scheduler_service.get_cyclic_tasks()
        for cycle_id, cycle_info in cyclic_task_list.items():
            try:
                # 检查循环任务的调度器作业状态
                on_job_id = cycle_info.get("on_job_id")
                off_job_id = cycle_info.get("off_job_id")
                
                # 通过检查作业的下次运行时间来判断当前阶段
                current_phase = "unknown"
                next_run_time = None
                
                if on_job_id and off_job_id:
                    try:
                        on_job = scheduler_service.scheduler.get_job(on_job_id)
                        off_job = scheduler_service.scheduler.get_job(off_job_id)
                        
                        if on_job and off_job:
                            on_next_run = on_job.next_run_time
                            off_next_run = off_job.next_run_time
                            
                            if on_next_run and off_next_run:
                                # 比较下次运行时间判断当前阶段
                                if on_next_run < off_next_run:
                                    current_phase = "off"  # 即将开启，说明当前是关闭状态
                                    next_run_time = on_next_run.strftime("%Y-%m-%d %H:%M:%S")
                                else:
                                    current_phase = "on"   # 即将关闭，说明当前是开启状态
                                    next_run_time = off_next_run.strftime("%Y-%m-%d %H:%M:%S")
                    except Exception:
                        current_phase = "unknown"
                
                cyclic_tasks.append({
                    "cycle_id": cycle_id,
                    "do_name": cycle_info.get("do_name", ""),
                    "on_minutes": cycle_info.get("on_minutes", 0),
                    "off_minutes": cycle_info.get("off_minutes", 0),
                    "status": cycle_info.get("status", "active"),
                    "current_phase": current_phase,
                    "next_run_time": next_run_time,
                    "device_sn": cycle_info.get("sn", "")
                })
            except Exception as e:
                # 跳过解析错误的任务
                continue
        
        # 获取顺序任务
        sequence_tasks = []
        sequence_task_list = scheduler_service.get_sequence_tasks()
        for sequence_id, sequence_info in sequence_task_list.items():
            try:
                # 检查顺序任务的健康状态
                health_check = scheduler_service.check_sequence_task_health(sequence_id)
                
                # 简单判断当前设备（基于作业的下次运行时间）
                current_device = "unknown"
                next_run_time = None
                
                job_ids = sequence_info.get("job_ids", [])
                if len(job_ids) >= 4:  # 应该有4个作业
                    try:
                        a_on_job = scheduler_service.scheduler.get_job(job_ids[0])  # A开启
                        b_on_job = scheduler_service.scheduler.get_job(job_ids[2])  # B开启
                        
                        if a_on_job and b_on_job:
                            a_next = a_on_job.next_run_time
                            b_next = b_on_job.next_run_time
                            
                            if a_next and b_next:
                                if a_next < b_next:
                                    current_device = "B"  # 即将启动A，说明当前是B
                                    next_run_time = a_next.strftime("%Y-%m-%d %H:%M:%S")
                                else:
                                    current_device = "A"  # 即将启动B，说明当前是A
                                    next_run_time = b_next.strftime("%Y-%m-%d %H:%M:%S")
                    except Exception:
                        current_device = "unknown"
                
                sequence_tasks.append({
                    "sequence_id": sequence_id,
                    "do_a_name": sequence_info.get("do_a_name", ""),
                    "do_b_name": sequence_info.get("do_b_name", ""),
                    "do_a_minutes": sequence_info.get("do_a_minutes", 0),
                    "do_b_minutes": sequence_info.get("do_b_minutes", 0),
                    "status": "healthy" if health_check["healthy"] else "error",
                    "current_device": current_device,
                    "next_run_time": next_run_time,
                    "device_sn": sequence_info.get("sn", ""),
                    "health_info": {
                        "healthy": health_check["healthy"],
                        "missing_jobs": len(health_check.get("missing_jobs", [])),
                        "total_jobs": health_check.get("total_jobs", 0)
                    }
                })
            except Exception as e:
                # 跳过解析错误的任务
                continue
        
        return {
            "success": True,
            "timestamp": current_time.isoformat(),
            "summary": {
                "single_tasks_count": len(single_tasks),
                "cyclic_tasks_count": len(cyclic_tasks),
                "sequence_tasks_count": len(sequence_tasks),
                "total_tasks": len(single_tasks) + len(cyclic_tasks) + len(sequence_tasks)
            },
            "single_tasks": single_tasks,
            "cyclic_tasks": cyclic_tasks,
            "sequence_tasks": sequence_tasks
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取任务状态汇总失败: {str(e)}"
        )


@app.get("/schedule/tasks/active", tags=["任务调度"], summary="获取活跃任务状态")
async def get_active_tasks_status():
    """
    获取当前正在执行或即将执行的活动任务详情。
    
    返回信息包括：
    - 任务ID和类型
    - 设备名称映射
    - 状态描述文本
    - 剩余时间
    - 下一步动作
    """
    try:
        current_time = datetime.datetime.now()
        active_tasks = []
        
        # 处理单次任务
        scheduled_tasks = scheduler_service.get_scheduled_tasks()
        for job_id, task_info in scheduled_tasks.items():
            try:
                execute_at_str = task_info.get("execute_at", "")
                if execute_at_str:
                    execute_at = datetime.datetime.strptime(execute_at_str, "%Y-%m-%d %H:%M:%S")
                    remaining_delta = execute_at - current_time
                    remaining_minutes = max(0, int(remaining_delta.total_seconds() / 60))
                    
                    # 转换DO名称为设备名称
                    do_name = task_info.get("do_name", "")
                    device_name_map = {
                        "DO21": "水泵1", "DO22": "水泵2",
                        "DO23": "气泵1", "DO24": "气泵2"
                    }
                    device_name = device_name_map.get(do_name, do_name)
                    
                    # 状态描述
                    value = task_info.get("value", 0)
                    action = "开启" if value == 1 else "关闭"
                    
                    if remaining_minutes > 0:
                        if remaining_minutes < 60:
                            time_text = f"{remaining_minutes}分钟后{action}"
                            remaining_time = f"{remaining_minutes}分钟"
                        else:
                            hours = remaining_minutes // 60
                            mins = remaining_minutes % 60
                            time_text = f"{hours}小时{mins}分钟后{action}" if mins > 0 else f"{hours}小时后{action}"
                            remaining_time = f"{hours}小时{mins}分钟" if mins > 0 else f"{hours}小时"
                    else:
                        time_text = f"即将{action}"
                        remaining_time = "即将执行"
                    
                    active_tasks.append({
                        "id": job_id,
                        "type": "single",
                        "device_name": device_name,
                        "status_text": time_text,
                        "remaining_time": remaining_time,
                        "next_action": action,
                        "device_sn": task_info.get("sn", ""),
                        "execute_at": execute_at_str
                    })
            except Exception:
                continue
        
        # 处理循环任务
        cyclic_task_list = scheduler_service.get_cyclic_tasks()
        for cycle_id, cycle_info in cyclic_task_list.items():
            try:
                do_name = cycle_info.get("do_name", "")
                device_name_map = {
                    "DO21": "水泵1", "DO22": "水泵2", 
                    "DO23": "气泵1", "DO24": "气泵2"
                }
                device_name = device_name_map.get(do_name, do_name)
                
                on_minutes = cycle_info.get("on_minutes", 0)
                off_minutes = cycle_info.get("off_minutes", 0)
                
                # 获取下次运行时间
                on_job_id = cycle_info.get("on_job_id")
                off_job_id = cycle_info.get("off_job_id")
                
                next_action = "未知"
                remaining_time = "运行中"
                status_text = f"{device_name}循环运行中（开{on_minutes}分钟，关{off_minutes}分钟）"
                
                if on_job_id and off_job_id:
                    try:
                        on_job = scheduler_service.scheduler.get_job(on_job_id)
                        off_job = scheduler_service.scheduler.get_job(off_job_id)
                        
                        if on_job and off_job:
                            on_next = on_job.next_run_time
                            off_next = off_job.next_run_time
                            
                            if on_next and off_next:
                                # 找到最近的下次运行时间
                                next_run = min(on_next, off_next)
                                remaining_delta = next_run - current_time.replace(tzinfo=next_run.tzinfo)
                                remaining_mins = max(0, int(remaining_delta.total_seconds() / 60))
                                
                                if next_run == on_next:
                                    next_action = "开启"
                                else:
                                    next_action = "关闭"
                                
                                if remaining_mins < 60:
                                    remaining_time = f"{remaining_mins}分钟"
                                    status_text = f"{device_name}将在{remaining_mins}分钟后{next_action}"
                                else:
                                    hours = remaining_mins // 60
                                    mins = remaining_mins % 60
                                    remaining_time = f"{hours}小时{mins}分钟" if mins > 0 else f"{hours}小时"
                                    status_text = f"{device_name}将在{remaining_time}后{next_action}"
                    except Exception:
                        pass
                
                active_tasks.append({
                    "id": cycle_id,
                    "type": "cycle",
                    "device_name": device_name,
                    "status_text": status_text,
                    "remaining_time": remaining_time,
                    "next_action": next_action,
                    "device_sn": cycle_info.get("sn", ""),
                    "cycle_info": {
                        "on_minutes": on_minutes,
                        "off_minutes": off_minutes
                    }
                })
            except Exception:
                continue
        
        # 处理顺序任务
        sequence_task_list = scheduler_service.get_sequence_tasks()
        for sequence_id, sequence_info in sequence_task_list.items():
            try:
                do_a_name = sequence_info.get("do_a_name", "")
                do_b_name = sequence_info.get("do_b_name", "")
                device_name_map = {
                    "DO21": "水泵1", "DO22": "水泵2", 
                    "DO23": "气泵1", "DO24": "气泵2"
                }
                device_a_name = device_name_map.get(do_a_name, do_a_name)
                device_b_name = device_name_map.get(do_b_name, do_b_name)
                
                do_a_minutes = sequence_info.get("do_a_minutes", 0)
                do_b_minutes = sequence_info.get("do_b_minutes", 0)
                
                # 检查健康状态
                health_check = scheduler_service.check_sequence_task_health(sequence_id)
                
                if not health_check["healthy"]:
                    status_text = f"顺序任务异常：{device_a_name}→{device_b_name}（{health_check['reason']}）"
                    next_action = "需要修复"
                    remaining_time = "异常"
                else:
                    status_text = f"顺序运行：{device_a_name}({do_a_minutes}分钟)→{device_b_name}({do_b_minutes}分钟)"
                    next_action = "循环运行"
                    remaining_time = "运行中"
                
                active_tasks.append({
                    "id": sequence_id,
                    "type": "sequence",
                    "device_name": f"{device_a_name}+{device_b_name}",
                    "status_text": status_text,
                    "remaining_time": remaining_time,
                    "next_action": next_action,
                    "device_sn": sequence_info.get("sn", ""),
                    "sequence_info": {
                        "device_a": device_a_name,
                        "device_b": device_b_name,
                        "do_a_minutes": do_a_minutes,
                        "do_b_minutes": do_b_minutes,
                        "healthy": health_check["healthy"]
                    }
                })
            except Exception:
                continue
        
        # 按剩余时间排序（即将执行的任务在前）
        def sort_key(task):
            if task["type"] == "single":
                try:
                    execute_at = datetime.datetime.strptime(task["execute_at"], "%Y-%m-%d %H:%M:%S")
                    return execute_at
                except:
                    return datetime.datetime.max
            else:
                return datetime.datetime.max  # 循环和顺序任务排在后面
        
        active_tasks.sort(key=sort_key)
        
        return {
            "success": True,
            "timestamp": current_time.isoformat(),
            "active_tasks_count": len(active_tasks),
            "active_tasks": active_tasks
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取活跃任务状态失败: {str(e)}"
        )


# -- 任务状态汇总接口结束 --


# -- 操作日志查询接口 --


@app.get("/operation-logs", tags=["操作日志"], summary="查询操作日志")
async def get_operation_logs(
    operation_type: Optional[str] = None,
    device_sn: Optional[str] = None,
    execution_status: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    search_keyword: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
):
    """
    查询系统操作日志记录，支持多种筛选条件。

    - **operation_type**: 过滤操作类型 (可选)，如 "pump_control", "device_command", "scheduler_task" 等
    - **device_sn**: 过滤设备序列号 (可选)
    - **execution_status**: 过滤执行状态 (可选)，如 "success", "failed", "pending"
    - **start_time**: 开始时间 (可选)，ISO格式，如 "2023-12-01T00:00:00Z"
    - **end_time**: 结束时间 (可选)，ISO格式，如 "2023-12-31T23:59:59Z"
    - **search_keyword**: 搜索关键字 (可选)，在操作详情、命令内容、错误信息中搜索
    - **skip**: 跳过的记录数
    - **limit**: 返回的最大记录数
    """
    result = crud.get_operation_logs(
        db=db,
        operation_type=operation_type,
        device_sn=device_sn,
        execution_status=execution_status,
        start_time=start_time,
        end_time=end_time,
        search_keyword=search_keyword,
        skip=skip,
        limit=limit,
    )

    # 格式化返回数据
    formatted_items = []
    for item in result["items"]:
        formatted_items.append(
            {
                "id": item.id,
                "timestamp": item.timestamp.isoformat(),
                "operation_type": item.operation_type,
                "device_sn": item.device_sn,
                "operation_details": item.operation_details,
                "command_sent": item.command_sent,
                "execution_status": item.execution_status,
                "error_message": item.error_message,
                "additional_data": item.additional_data,
            }
        )

    return {
        "total": result["total"],
        "items": formatted_items,
        "skip": skip,
        "limit": limit,
    }


@app.get("/operation-logs/pump-control", tags=["操作日志"], summary="查询水泵控制日志")
async def get_pump_control_logs(
    device_sn: Optional[str] = None,
    execution_status: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    search_keyword: Optional[str] = None,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db),
):
    """
    专门查询水泵控制相关的操作日志，支持多种筛选条件。
    
    - **device_sn**: 过滤设备序列号 (可选)
    - **execution_status**: 过滤执行状态 (可选)，如 "success", "failed", "pending"
    - **start_time**: 开始时间 (可选)，ISO格式，如 "2023-12-01T00:00:00Z"
    - **end_time**: 结束时间 (可选)，ISO格式，如 "2023-12-31T23:59:59Z"
    - **search_keyword**: 搜索关键字 (可选)，在操作详情、命令内容、错误信息中搜索
    - **skip**: 跳过的记录数
    - **limit**: 返回的最大记录数
    """
    result = crud.get_operation_logs(
        db=db,
        operation_type="pump_control",
        device_sn=device_sn,
        execution_status=execution_status,
        start_time=start_time,
        end_time=end_time,
        search_keyword=search_keyword,
        skip=skip,
        limit=limit,
    )

    # 格式化返回数据
    formatted_items = []
    for item in result["items"]:
        formatted_items.append(
            {
                "id": item.id,
                "timestamp": item.timestamp.isoformat(),
                "device_sn": item.device_sn,
                "operation_details": item.operation_details,
                "execution_status": item.execution_status,
                "error_message": item.error_message,
                "pump_info": item.additional_data,
            }
        )

    return {
        "total": result["total"],
        "items": formatted_items,
        "skip": skip,
        "limit": limit,
    }


# -- 操作日志查询接口结束 --


# -- 设备延时配置接口 --


@app.get("/device-timing-config", tags=["系统配置"], summary="获取设备延时配置")
async def get_device_timing_config():
    """
    获取当前的设备延时配置参数。
    """
    from device_timing import DEVICE_TIMING_CONFIG

    return {
        "config": DEVICE_TIMING_CONFIG,
        "descriptions": {
            "relay_response_delay": "继电器响应延时（秒）",
            "pump_stop_delay": "水泵停止后的等待时间（秒）",
            "pump_start_delay": "水泵启动前的等待时间（秒）",
            "command_interval": "同一设备命令间隔（秒）",
            "force_switch_delay": "强制轮换时的延时（秒）",
        },
    }


class TimingConfigUpdate(BaseModel):
    """延时配置更新请求模型"""

    relay_response_delay: float = Field(ge=0, le=60, description="继电器响应延时（秒）")
    pump_stop_delay: float = Field(
        ge=0, le=60, description="水泵停止后的等待时间（秒）"
    )
    pump_start_delay: float = Field(
        ge=0, le=60, description="水泵启动前的等待时间（秒）"
    )
    command_interval: float = Field(ge=0, le=10, description="同一设备命令间隔（秒）")
    force_switch_delay: float = Field(ge=0, le=60, description="强制轮换时的延时（秒）")


@app.post("/device-timing-config", tags=["系统配置"], summary="更新设备延时配置")
async def update_device_timing_config(config: TimingConfigUpdate):
    """
    更新设备延时配置参数。
    """
    from device_timing import DEVICE_TIMING_CONFIG

    # 更新配置
    DEVICE_TIMING_CONFIG.update(config.model_dump())

    return {"message": "设备延时配置已更新", "new_config": DEVICE_TIMING_CONFIG}


# -- 设备延时配置接口结束 --


# -- 状态监控管理接口 --


@app.get("/state-monitor/status", tags=["状态监控"], summary="获取状态监控状态")
async def get_state_monitor_status():
    """
    获取状态监控的运行状态和配置信息。
    """
    from state_monitor import get_state_monitor_status

    status = get_state_monitor_status()
    return {
        "monitor_status": status,
        "config_descriptions": {
            "enabled": "是否启用状态监控",
            "check_interval_seconds": "检查间隔（秒）",
            "auto_repair_enabled": "是否启用自动修复",
            "repair_retry_count": "修复重试次数",
            "repair_retry_delay": "修复重试延时（秒）",
            "float_timeout_check": "是否检查浮球超时",
            "max_pump_runtime_hours": "最大水泵运行时间（小时）",
        },
    }


class StateMonitorConfig(BaseModel):
    """状态监控配置模型"""

    enabled: bool = Field(description="是否启用状态监控")
    check_interval_seconds: int = Field(ge=10, le=3600, description="检查间隔（秒）")
    auto_repair_enabled: bool = Field(description="是否启用自动修复")
    repair_retry_count: int = Field(ge=1, le=10, description="修复重试次数")
    repair_retry_delay: int = Field(ge=1, le=300, description="修复重试延时（秒）")
    float_timeout_check: bool = Field(description="是否检查浮球超时")
    max_pump_runtime_hours: int = Field(
        ge=0, le=24, description="最大水泵运行时间（小时）"
    )


class PumpConfigUpdate(BaseModel):
    """水泵配置更新模型"""
    
    pump2_start_delay_hours: float = Field(
        ge=0.1, le=24.0, description="水泵1运行多久后启动水泵2（小时）"
    )


@app.post("/state-monitor/config", tags=["状态监控"], summary="更新状态监控配置")
async def update_state_monitor_config(config: StateMonitorConfig):
    """
    更新状态监控配置参数。
    """
    from state_monitor import update_state_monitor_config

    # 更新配置
    new_config = update_state_monitor_config(config.model_dump())

    return {"message": "状态监控配置已更新", "new_config": new_config}


@app.post("/state-monitor/manual-check", tags=["状态监控"], summary="手动触发状态检查")
async def manual_state_check(
    device_sn: Optional[str] = None, db: Session = Depends(get_db)
):
    """
    手动触发一次状态检查。

    - device_sn: 指定设备序列号，如果为空则检查所有设备
    """
    from state_monitor import _state_monitor

    # 记录手动检查操作
    check_log = crud.create_operation_log(
        db=db,
        operation_type="manual_state_check",
        operation_details=f"手动触发状态检查，设备: {device_sn or '所有设备'}",
        device_sn=device_sn,
        execution_status="pending",
    )

    try:
        if device_sn:
            # 检查指定设备
            issues = _state_monitor._check_device_state(db, device_sn)
            repairs = []

            from state_monitor import STATE_MONITOR_CONFIG

            if issues and STATE_MONITOR_CONFIG["auto_repair_enabled"]:
                repairs = _state_monitor._repair_device_state(db, device_sn, issues)

            crud.update_operation_log_status(db, check_log.id, "success")

            return {
                "message": f"设备 {device_sn} 状态检查完成",
                "device_sn": device_sn,
                "issues_found": len(issues),
                "repairs_attempted": len(repairs),
                "details": {"issues": issues, "repairs": repairs},
            }
        else:
            # 触发完整的状态检查
            _state_monitor._perform_state_check()
            crud.update_operation_log_status(db, check_log.id, "success")

            return {"message": "全设备状态检查已触发", "note": "详细结果请查看操作日志"}

    except Exception as e:
        error_msg = f"手动状态检查失败: {e}"
        crud.update_operation_log_status(db, check_log.id, "failed", error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@app.get("/state-monitor/logs", tags=["状态监控"], summary="获取状态监控日志")
async def get_state_monitor_logs(
    skip: int = 0, limit: int = 50, db: Session = Depends(get_db)
):
    """
    获取状态监控相关的操作日志。
    """
    # 查询状态监控相关的日志
    monitor_logs = crud.get_operation_logs(
        db=db, operation_type="state_monitor", skip=skip, limit=limit
    )

    repair_logs = crud.get_operation_logs(
        db=db, operation_type="state_repair", skip=skip, limit=limit
    )

    check_logs = crud.get_operation_logs(
        db=db, operation_type="manual_state_check", skip=skip, limit=limit
    )

    # 格式化返回数据
    def format_log_items(logs):
        formatted = []
        for item in logs["items"]:
            formatted.append(
                {
                    "id": item.id,
                    "timestamp": item.timestamp.isoformat(),
                    "operation_type": item.operation_type,
                    "device_sn": item.device_sn,
                    "operation_details": item.operation_details,
                    "execution_status": item.execution_status,
                    "error_message": item.error_message,
                    "additional_data": item.additional_data,
                }
            )
        return formatted

    return {
        "monitor_logs": {
            "total": monitor_logs["total"],
            "items": format_log_items(monitor_logs),
        },
        "repair_logs": {
            "total": repair_logs["total"],
            "items": format_log_items(repair_logs),
        },
        "check_logs": {
            "total": check_logs["total"],
            "items": format_log_items(check_logs),
        },
    }


# -- 状态监控管理接口结束 --

# -- 气泵状态管理接口开始 --

@app.post("/air-pump/{sn}/start", tags=["设备控制"], summary="启动气泵")
async def start_air_pump(
    sn: str,
    pump_name: str = Query(..., description="气泵名称 (air_pump1 或 air_pump2)"),
    db: Session = Depends(get_db)
):
    """
    启动指定设备的气泵并记录状态。
    """
    # 禁止对M300设备进行气泵控制（M300本地自主控制）
    if sn.startswith("02800125"):
        raise HTTPException(
            status_code=403,
            detail=f"M300设备 '{sn}' 采用本地自主控制，不支持远程气泵控制。",
        )
    
    if pump_name not in ["air_pump1", "air_pump2"]:
        raise HTTPException(status_code=400, detail="气泵名称必须是 air_pump1 或 air_pump2")
    
    # 检查设备是否连接
    with data_store.CLIENTS_LOCK:
        if sn not in data_store.CONNECTED_CLIENTS:
            raise HTTPException(status_code=404, detail=f"设备 {sn} 未连接")
    
    # 检查气泵是否处于自动模式
    with data_store.DATA_LOCK:
        device_data = data_store.DEVICE_DATA.get(sn, {})
    
    if device_data.get(pump_name, {}).get("auto_status") != 1:
        raise HTTPException(status_code=400, detail=f"气泵 {pump_name} 未处于自动模式")
    
    # 停止其他气泵
    for other_pump in ["air_pump1", "air_pump2"]:
        if other_pump != pump_name:
            other_do = {"air_pump1": "DO23", "air_pump2": "DO24"}[other_pump]
            if device_data.get(f"{other_do}_status") == 1:
                stop_command = create_do_command(do_name=other_do, value=0)
                send_command_to_device(sn=sn, command=stop_command)
    
    # 启动指定气泵
    do_name = {"air_pump1": "DO23", "air_pump2": "DO24"}[pump_name]
    
    # 记录操作日志
    pump_log = crud.create_operation_log(
        db=db,
        operation_type="air_pump_control",
        operation_details=f"手动启动气泵 {pump_name} (DO: {do_name})",
        device_sn=sn,
        additional_data={
            "pump_name": pump_name,
            "do_name": do_name,
            "trigger": "manual_start",
        }
    )
    
    # 发送启动命令
    wait_for_device_ready(sn)
    command = create_do_command(do_name=do_name, value=1)
    success, cmd_log_id = send_command_to_device(sn=sn, command=command)
    
    if success:
        # 记录气泵状态到数据库
        active_air_pump_key = f"active_air_pump_{sn}"
        air_pump_start_time_key = f"air_pump_start_time_{sn}"
        current_time = datetime.datetime.now()
        
        crud.set_kv(db, active_air_pump_key, pump_name)
        crud.set_kv(db, air_pump_start_time_key, current_time.isoformat())
        
        crud.update_operation_log_status(db, pump_log.id, "success")
        
        return {
            "message": f"气泵 {pump_name} 启动成功",
            "pump_name": pump_name,
            "do_name": do_name,
            "start_time": current_time.isoformat(),
            "success": True
        }
    else:
        crud.update_operation_log_status(db, pump_log.id, "failed", "设备命令发送失败")
        raise HTTPException(status_code=500, detail="气泵启动失败")


@app.post("/air-pump/{sn}/stop", tags=["设备控制"], summary="停止气泵")
async def stop_air_pump(
    sn: str,
    pump_name: str = Query(..., description="气泵名称 (air_pump1 或 air_pump2)"),
    db: Session = Depends(get_db)
):
    """
    停止指定设备的气泵。
    """
    # 禁止对M300设备进行气泵控制（M300本地自主控制）
    if sn.startswith("02800125"):
        raise HTTPException(
            status_code=403,
            detail=f"M300设备 '{sn}' 采用本地自主控制，不支持远程气泵控制。",
        )
    
    if pump_name not in ["air_pump1", "air_pump2"]:
        raise HTTPException(status_code=400, detail="气泵名称必须是 air_pump1 或 air_pump2")
    
    # 检查设备是否连接
    with data_store.CLIENTS_LOCK:
        if sn not in data_store.CONNECTED_CLIENTS:
            raise HTTPException(status_code=404, detail=f"设备 {sn} 未连接")
    
    do_name = {"air_pump1": "DO23", "air_pump2": "DO24"}[pump_name]
    
    # 记录操作日志
    pump_log = crud.create_operation_log(
        db=db,
        operation_type="air_pump_control",
        operation_details=f"手动停止气泵 {pump_name} (DO: {do_name})",
        device_sn=sn,
        additional_data={
            "pump_name": pump_name,
            "do_name": do_name,
            "trigger": "manual_stop",
        }
    )
    
    # 发送停止命令
    wait_for_device_ready(sn)
    command = create_do_command(do_name=do_name, value=0)
    success, cmd_log_id = send_command_to_device(sn=sn, command=command)
    
    if success:
        # 清理数据库状态
        active_air_pump_key = f"active_air_pump_{sn}"
        air_pump_start_time_key = f"air_pump_start_time_{sn}"
        crud.set_kv(db, active_air_pump_key, "")
        crud.set_kv(db, air_pump_start_time_key, "")
        
        crud.update_operation_log_status(db, pump_log.id, "success")
        
        return {
            "message": f"气泵 {pump_name} 停止成功",
            "pump_name": pump_name,
            "do_name": do_name,
            "success": True
        }
    else:
        crud.update_operation_log_status(db, pump_log.id, "failed", "设备命令发送失败")
        raise HTTPException(status_code=500, detail="气泵停止失败")


@app.get("/air-pump/{sn}/status", tags=["设备控制"], summary="获取气泵状态")
async def get_air_pump_status(
    sn: str,
    db: Session = Depends(get_db)
):
    """
    获取指定设备的气泵状态信息。
    """
    # 检查设备是否连接
    with data_store.CLIENTS_LOCK:
        if sn not in data_store.CONNECTED_CLIENTS:
            raise HTTPException(status_code=404, detail=f"设备 {sn} 未连接")
    
    # 获取设备实时数据
    with data_store.DATA_LOCK:
        device_data = data_store.DEVICE_DATA.get(sn, {})
    
    # 获取数据库中的气泵状态
    active_air_pump_key = f"active_air_pump_{sn}"
    air_pump_start_time_key = f"air_pump_start_time_{sn}"
    
    expected_active_pump = crud.get_kv(db, active_air_pump_key)
    start_time_str = crud.get_kv(db, air_pump_start_time_key)
    
    # 计算运行时间
    runtime_hours = None
    if start_time_str:
        try:
            start_time = datetime.datetime.fromisoformat(start_time_str)
            runtime = datetime.datetime.now() - start_time
            runtime_hours = runtime.total_seconds() / 3600
        except ValueError:
            pass
    
    return {
        "device_sn": sn,
        "air_pump_status": {
            "air_pump1": {
                "actual_status": device_data.get("DO23_status", 0),
                "auto_status": device_data.get("air_pump1", {}).get("auto_status", 0),
                "is_expected_active": expected_active_pump == "air_pump1",
            },
            "air_pump2": {
                "actual_status": device_data.get("DO24_status", 0),
                "auto_status": device_data.get("air_pump2", {}).get("auto_status", 0),
                "is_expected_active": expected_active_pump == "air_pump2",
            }
        },
        "expected_active_pump": expected_active_pump,
        "start_time": start_time_str,
        "runtime_hours": runtime_hours,
        "timestamp": datetime.datetime.now().isoformat()
    }


# -- 气泵状态管理接口结束 --


# -- 设备状态恢复管理接口开始 --

@app.post("/device/{sn}/recover-state", tags=["设备状态"], summary="手动触发设备状态恢复")
async def manual_device_state_recovery(
    sn: str,
    db: Session = Depends(get_db)
):
    """
    手动触发指定设备的状态恢复检查。
    适用于设备重连或需要手动同步状态的场景。
    """
    # 禁止对M300设备进行状态恢复（M300本地自主控制）
    if sn.startswith("02800125"):
        raise HTTPException(
            status_code=403,
            detail=f"M300设备 '{sn}' 采用本地自主控制，不需要状态恢复。",
        )
    
    # 检查设备是否连接
    with data_store.CLIENTS_LOCK:
        if sn not in data_store.CONNECTED_CLIENTS:
            raise HTTPException(status_code=404, detail=f"设备 {sn} 未连接")
    
    # 获取设备当前数据
    with data_store.DATA_LOCK:
        device_data = data_store.DEVICE_DATA.get(sn, {})
    
    if not device_data:
        raise HTTPException(status_code=400, detail=f"设备 {sn} 暂无数据")
    
    # 记录操作日志
    recovery_log = crud.create_operation_log(
        db=db,
        operation_type="manual_state_recovery",
        operation_details=f"手动触发设备 {sn} 状态恢复检查",
        device_sn=sn,
        additional_data={
            "trigger": "manual",
            "device_data_keys": list(device_data.keys())
        }
    )
    
    try:
        # 执行状态恢复
        recovery_performed = check_and_recover_device_state(sn, device_data)
        
        crud.update_operation_log_status(db, recovery_log.id, "success")
        
        return {
            "message": f"设备 {sn} 状态恢复检查完成",
            "device_sn": sn,
            "recovery_performed": recovery_performed,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        error_msg = f"状态恢复失败: {e}"
        crud.update_operation_log_status(db, recovery_log.id, "failed", error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@app.get("/device/{sn}/recovery-status", tags=["设备状态"], summary="获取设备状态恢复信息")
async def get_device_recovery_status(
    sn: str,
    db: Session = Depends(get_db)
):
    """
    获取设备的状态恢复相关信息，包括数据库预期状态和设备实际状态的对比。
    """
    # 检查设备是否连接
    with data_store.CLIENTS_LOCK:
        if sn not in data_store.CONNECTED_CLIENTS:
            raise HTTPException(status_code=404, detail=f"设备 {sn} 未连接")
    
    # 获取设备当前数据
    with data_store.DATA_LOCK:
        device_data = data_store.DEVICE_DATA.get(sn, {})
    
    # 获取数据库中的预期状态
    active_pump_key = f"active_pump_{sn}"
    pump_start_time_key = f"pump_start_time_{sn}"
    active_air_pump_key = f"active_air_pump_{sn}"
    air_pump_start_time_key = f"air_pump_start_time_{sn}"
    
    expected_water_pump = crud.get_kv(db, active_pump_key)
    water_pump_start_time = crud.get_kv(db, pump_start_time_key)
    expected_air_pump = crud.get_kv(db, active_air_pump_key)
    air_pump_start_time = crud.get_kv(db, air_pump_start_time_key)
    
    # 分析状态差异
    def check_pump_consistency(expected_pump, start_time, pump1_status, pump2_status, pump_type):
        if not expected_pump or not start_time:
            return {
                "expected": None,
                "actual": {"pump1": pump1_status, "pump2": pump2_status},
                "consistent": True,
                "needs_recovery": False
            }
        
        expected_status = {"pump1": 0, "pump2": 0}
        if expected_pump.endswith("1"):
            expected_status["pump1"] = 1
        elif expected_pump.endswith("2"):
            expected_status["pump2"] = 1
            
        actual_status = {"pump1": pump1_status, "pump2": pump2_status}
        consistent = (expected_status == actual_status)
        
        return {
            "expected": expected_pump,
            "expected_status": expected_status,
            "actual_status": actual_status,
            "start_time": start_time,
            "consistent": consistent,
            "needs_recovery": not consistent
        }
    
    # 检查水泵状态
    water_pump_analysis = check_pump_consistency(
        expected_water_pump,
        water_pump_start_time,
        device_data.get("DO21_status", 0),
        device_data.get("DO22_status", 0),
        "water_pump"
    )
    
    # 检查气泵状态
    air_pump_analysis = check_pump_consistency(
        expected_air_pump,
        air_pump_start_time,
        device_data.get("DO23_status", 0),
        device_data.get("DO24_status", 0),
        "air_pump"
    )
    
    # 获取最近的状态恢复日志
    recent_recovery_logs = crud.get_operation_logs(
        db=db, 
        operation_type="state_recovery", 
        device_sn=sn,
        skip=0, 
        limit=5
    )
    
    return {
        "device_sn": sn,
        "timestamp": datetime.datetime.now().isoformat(),
        "device_connected": True,
        "float1_status": device_data.get("float_switches", {}).get("float1", 0),
        "water_pump_analysis": water_pump_analysis,
        "air_pump_analysis": air_pump_analysis,
        "overall_needs_recovery": water_pump_analysis["needs_recovery"] or air_pump_analysis["needs_recovery"],
        "recent_recovery_logs": {
            "total": recent_recovery_logs["total"],
            "items": [
                {
                    "timestamp": log.timestamp.isoformat(),
                    "operation_details": log.operation_details,
                    "execution_status": log.execution_status,
                    "error_message": log.error_message,
                    "additional_data": log.additional_data
                }
                for log in recent_recovery_logs["items"]
            ]
        }
    }


@app.get("/state-recovery/logs", tags=["设备状态"], summary="获取所有设备状态恢复日志")
async def get_all_recovery_logs(
    skip: int = 0, 
    limit: int = 50, 
    db: Session = Depends(get_db)
):
    """
    获取所有设备的状态恢复相关日志。
    """
    # 查询状态恢复相关的日志
    recovery_logs = crud.get_operation_logs(
        db=db, operation_type="state_recovery", skip=skip, limit=limit
    )
    
    manual_recovery_logs = crud.get_operation_logs(
        db=db, operation_type="manual_state_recovery", skip=skip, limit=limit
    )
    
    # 格式化返回数据
    def format_recovery_logs(logs):
        formatted = []
        for item in logs["items"]:
            formatted.append({
                "id": item.id,
                "timestamp": item.timestamp.isoformat(),
                "operation_type": item.operation_type,
                "device_sn": item.device_sn,
                "operation_details": item.operation_details,
                "execution_status": item.execution_status,
                "error_message": item.error_message,
                "additional_data": item.additional_data,
            })
        return formatted
    
    return {
        "automatic_recovery_logs": {
            "total": recovery_logs["total"],
            "items": format_recovery_logs(recovery_logs),
        },
        "manual_recovery_logs": {
            "total": manual_recovery_logs["total"],
            "items": format_recovery_logs(manual_recovery_logs),
        },
        "timestamp": datetime.datetime.now().isoformat()
    }


# -- 设备状态恢复管理接口结束 --


# -- 水泵配置管理接口 --

@app.get("/pump-config", tags=["系统配置"], summary="获取水泵配置")
async def get_pump_config(db: Session = Depends(get_db)):
    """
    获取当前的水泵配置参数。
    """
    # 获取水泵2启动延时配置，默认2小时
    pump2_delay = crud.get_kv(db, "pump2_start_delay_hours") or "2.0"
    
    try:
        pump2_delay_float = float(pump2_delay)
    except ValueError:
        pump2_delay_float = 2.0
    
    return {
        "config": {
            "pump2_start_delay_hours": pump2_delay_float,
        },
        "descriptions": {
            "pump2_start_delay_hours": "水泵1运行多久后启动水泵2（小时）",
        },
        "timestamp": datetime.datetime.now().isoformat()
    }


@app.put("/pump-config", tags=["系统配置"], summary="更新水泵配置")
async def update_pump_config(config: PumpConfigUpdate, db: Session = Depends(get_db)):
    """
    更新水泵配置参数。
    """
    # 更新数据库中的配置值
    crud.set_kv(db, "pump2_start_delay_hours", str(config.pump2_start_delay_hours))
    
    # 记录配置更新操作日志
    crud.create_operation_log(
        db=db,
        operation_type="config_update",
        operation_details=f"更新水泵配置: 水泵2启动延时改为 {config.pump2_start_delay_hours} 小时",
        additional_data={
            "config_key": "pump2_start_delay_hours",
            "new_value": config.pump2_start_delay_hours,
            "config_type": "pump_config"
        }
    )
    
    return {
        "message": "水泵配置已更新",
        "new_config": {
            "pump2_start_delay_hours": config.pump2_start_delay_hours,
        },
        "timestamp": datetime.datetime.now().isoformat()
    }


@app.delete("/pump-config", tags=["系统配置"], summary="重置水泵配置为默认值")
async def reset_pump_config(db: Session = Depends(get_db)):
    """
    重置水泵配置为默认值（2小时）。
    """
    default_delay = 2.0
    
    # 重置为默认值
    crud.set_kv(db, "pump2_start_delay_hours", str(default_delay))
    
    # 记录重置操作日志
    crud.create_operation_log(
        db=db,
        operation_type="config_reset",
        operation_details="重置水泵配置为默认值: 水泵2启动延时恢复为 2.0 小时",
        additional_data={
            "config_key": "pump2_start_delay_hours",
            "reset_value": default_delay,
            "config_type": "pump_config"
        }
    )
    
    return {
        "message": "水泵配置已重置为默认值",
        "default_config": {
            "pump2_start_delay_hours": default_delay,
        },
        "timestamp": datetime.datetime.now().isoformat()
    }

# -- 水泵配置管理接口结束 --

# == M300配置管理接口 ==

# M300配置文件存储路径
M300_CONFIG_DIR = "/home/<USER>/tcp-server/m300-configs"
CURRENT_VERSION_FILE = "/home/<USER>/tcp-server/m300-current-version.json"

# 确保目录存在
os.makedirs(M300_CONFIG_DIR, exist_ok=True)

def get_current_m300_version():
    """获取当前M300配置版本"""
    try:
        if os.path.exists(CURRENT_VERSION_FILE):
            with open(CURRENT_VERSION_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('version', '1.0.0')
        return '1.0.0'
    except Exception:
        return '1.0.0'

def set_current_m300_version(version: str):
    """设置当前M300配置版本"""
    try:
        data = {
            'version': version,
            'updated_at': datetime.datetime.now().isoformat()
        }
        with open(CURRENT_VERSION_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception:
        return False

def get_available_m300_versions():
    """获取所有可用的M300配置版本"""
    try:
        versions = []
        config_dir = Path(M300_CONFIG_DIR)
        for file_path in config_dir.glob("*.json"):
            if file_path.name.endswith('.json'):
                # 从文件名提取版本号，格式: m300-config-v{version}.json
                filename = file_path.stem
                if filename.startswith('m300-config-v'):
                    version = filename.replace('m300-config-v', '')
                    versions.append({
                        'version': version,
                        'file_path': str(file_path),
                        'modified_time': datetime.datetime.fromtimestamp(
                            file_path.stat().st_mtime
                        ).isoformat()
                    })
        
        # 按版本号排序
        versions.sort(key=lambda x: x['version'], reverse=True)
        return versions
    except Exception as e:
        return []

@app.get("/m300/config/latest", tags=["M300配置"], summary="获取最新M300配置信息")
async def get_latest_m300_config():
    """
    获取最新的M300配置版本信息。
    
    返回信息包括：
    - 当前版本号
    - 最新可用版本号
    - 是否有可用更新
    - 最后更新时间
    """
    try:
        current_version = get_current_m300_version()
        available_versions = get_available_m300_versions()
        
        latest_version = available_versions[0]['version'] if available_versions else current_version
        has_update = latest_version != current_version
        
        return {
            "success": True,
            "current_version": current_version,
            "latest_version": latest_version,
            "has_update": has_update,
            "available_versions": len(available_versions),
            "last_check": datetime.datetime.now().isoformat(),
            "server_info": {
                "host": "**************",
                "port": 8500,
                "tcp_port": 8889
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取M300配置信息失败: {str(e)}"
        )

@app.get("/m300/config/download/{version}", tags=["M300配置"], summary="下载指定版本的M300配置")
async def download_m300_config(version: str):
    """
    下载指定版本的M300配置文件。
    
    - **version**: 版本号，如 "1.0.0"
    
    返回完整的Node-RED流程JSON配置。
    """
    try:
        # 构建配置文件路径
        config_file = os.path.join(M300_CONFIG_DIR, f"m300-config-v{version}.json")
        
        if not os.path.exists(config_file):
            raise HTTPException(
                status_code=404,
                detail=f"版本 {version} 的配置文件不存在"
            )
        
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 验证配置格式
        if not isinstance(config_data, dict) or 'flows' not in config_data:
            raise HTTPException(
                status_code=500,
                detail=f"版本 {version} 的配置文件格式错误"
            )
        
        return {
            "success": True,
            "version": version,
            "flows": config_data['flows'],
            "metadata": config_data.get('metadata', {}),
            "download_time": datetime.datetime.now().isoformat(),
            "flow_count": len(config_data['flows']) if isinstance(config_data['flows'], list) else 0
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"下载配置文件失败: {str(e)}"
        )

class M300ConfigUpload(BaseModel):
    version: str = Field(..., description="版本号")
    flows: list = Field(..., description="Node-RED流程配置")
    metadata: Optional[dict] = Field(None, description="配置元数据")
    description: Optional[str] = Field(None, description="版本描述")

@app.post("/m300/config/upload", tags=["M300配置"], summary="上传新的M300配置版本")
async def upload_m300_config(
    config: M300ConfigUpload,
    db: Session = Depends(get_db)
):
    """
    上传新的M300配置版本。
    
    此操作会：
    1. 验证配置格式
    2. 保存配置文件
    3. 记录操作日志
    
    - **version**: 版本号（如 "1.0.1"）
    - **flows**: Node-RED流程配置数组
    - **metadata**: 配置元数据（可选）
    - **description**: 版本描述（可选）
    """
    try:
        # 验证版本号格式
        if not config.version or not config.version.replace('.', '').replace('-', '').isalnum():
            raise HTTPException(
                status_code=400,
                detail="版本号格式错误"
            )
        
        # 检查版本是否已存在
        config_file = os.path.join(M300_CONFIG_DIR, f"m300-config-v{config.version}.json")
        if os.path.exists(config_file):
            raise HTTPException(
                status_code=409,
                detail=f"版本 {config.version} 已存在"
            )
        
        # 准备配置数据
        config_data = {
            "version": config.version,
            "flows": config.flows,
            "metadata": {
                "created_at": datetime.datetime.now().isoformat(),
                "description": config.description,
                "flow_count": len(config.flows),
                **(config.metadata or {})
            }
        }
        
        # 保存配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        # 记录操作日志
        crud.create_operation_log(
            db=db,
            operation_type="m300_config_upload",
            operation_details=f"上传M300配置版本: {config.version}",
            additional_data={
                "version": config.version,
                "flow_count": len(config.flows),
                "description": config.description,
                "file_size": os.path.getsize(config_file)
            }
        )
        
        return {
            "success": True,
            "message": f"M300配置版本 {config.version} 上传成功",
            "version": config.version,
            "file_path": config_file,
            "flow_count": len(config.flows),
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"上传配置失败: {str(e)}"
        )

@app.post("/m300/config/activate/{version}", tags=["M300配置"], summary="激活指定版本的M300配置")
async def activate_m300_config(
    version: str,
    db: Session = Depends(get_db)
):
    """
    激活指定版本的M300配置为当前版本。
    
    - **version**: 要激活的版本号
    """
    try:
        # 检查版本是否存在
        config_file = os.path.join(M300_CONFIG_DIR, f"m300-config-v{version}.json")
        if not os.path.exists(config_file):
            raise HTTPException(
                status_code=404,
                detail=f"版本 {version} 不存在"
            )
        
        # 更新当前版本
        if not set_current_m300_version(version):
            raise HTTPException(
                status_code=500,
                detail="更新当前版本失败"
            )
        
        # 记录操作日志
        crud.create_operation_log(
            db=db,
            operation_type="m300_config_activate",
            operation_details=f"激活M300配置版本: {version}",
            additional_data={
                "activated_version": version
            }
        )
        
        return {
            "success": True,
            "message": f"M300配置版本 {version} 已激活",
            "current_version": version,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"激活配置版本失败: {str(e)}"
        )

@app.get("/m300/config/versions", tags=["M300配置"], summary="获取所有M300配置版本列表")
async def get_m300_config_versions():
    """
    获取所有可用的M300配置版本列表。
    """
    try:
        current_version = get_current_m300_version()
        available_versions = get_available_m300_versions()
        
        return {
            "success": True,
            "current_version": current_version,
            "versions": available_versions,
            "total_count": len(available_versions),
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取版本列表失败: {str(e)}"
        )

@app.delete("/m300/config/versions/{version}", tags=["M300配置"], summary="删除指定版本的M300配置")
async def delete_m300_config_version(
    version: str,
    db: Session = Depends(get_db)
):
    """
    删除指定版本的M300配置文件。
    
    - **version**: 要删除的版本号
    """
    try:
        # 检查是否为当前版本
        current_version = get_current_m300_version()
        if version == current_version:
            raise HTTPException(
                status_code=400,
                detail="无法删除当前激活的版本"
            )
        
        # 检查版本是否存在
        config_file = os.path.join(M300_CONFIG_DIR, f"m300-config-v{version}.json")
        if not os.path.exists(config_file):
            raise HTTPException(
                status_code=404,
                detail=f"版本 {version} 不存在"
            )
        
        # 删除配置文件
        os.remove(config_file)
        
        # 记录操作日志
        crud.create_operation_log(
            db=db,
            operation_type="m300_config_delete",
            operation_details=f"删除M300配置版本: {version}",
            additional_data={
                "deleted_version": version
            }
        )
        
        return {
            "success": True,
            "message": f"M300配置版本 {version} 已删除",
            "deleted_version": version,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除配置版本失败: {str(e)}"
        )

# == M300配置管理接口结束 ==


# == Node-RED配置管理接口 ==

# Node-RED配置文件相关路径
NODE_RED_CONFIG_DIR = "/home/<USER>/tcp-server/node-red-configs"
NODE_RED_FLOWS_FILE = "/home/<USER>/tcp-server/merged-flows.json"

# 确保目录存在
os.makedirs(NODE_RED_CONFIG_DIR, exist_ok=True)

@app.get("/node-red-config/latest", tags=["Node-RED配置"], summary="获取最新Node-RED配置")
async def get_latest_node_red_config():
    """
    获取最新的Node-RED配置文件内容。
    
    M300设备通过此接口获取最新的配置并自动更新流程。
    
    返回信息包括：
    - 配置版本号
    - 流程配置内容
    - 更新时间
    - 配置校验信息
    """
    try:
        # 检查配置文件是否存在
        if not os.path.exists(NODE_RED_FLOWS_FILE):
            raise HTTPException(
                status_code=404,
                detail="Node-RED配置文件不存在"
            )
        
        # 读取配置文件
        with open(NODE_RED_FLOWS_FILE, 'r', encoding='utf-8') as f:
            flows_content = f.read()
        
        # 尝试解析JSON以验证格式
        try:
            flows_data = json.loads(flows_content)
        except json.JSONDecodeError as e:
            raise HTTPException(
                status_code=500,
                detail=f"配置文件格式错误: {str(e)}"
            )
        
        # 获取文件修改时间作为版本号
        file_stat = os.stat(NODE_RED_FLOWS_FILE)
        version = datetime.datetime.fromtimestamp(file_stat.st_mtime).strftime("%Y%m%d_%H%M%S")
        
        # 统计流程信息
        flow_count = len(flows_data) if isinstance(flows_data, list) else 0
        tab_count = sum(1 for node in flows_data if isinstance(node, dict) and node.get('type') == 'tab')
        
        return {
            "success": True,
            "version": version,
            "flows": flows_data,
            "metadata": {
                "file_size": file_stat.st_size,
                "modified_time": datetime.datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                "flow_count": flow_count,
                "tab_count": tab_count,
                "checksum": hash(flows_content) % (10**8)  # 简单校验和
            },
            "download_time": datetime.datetime.now().isoformat(),
            "server_info": {
                "host": "**************",
                "port": 8500,
                "tcp_port": 8889
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取Node-RED配置失败: {str(e)}"
        )


@app.get("/node-red-config/flows-only", tags=["Node-RED配置"], summary="仅获取Node-RED流程数据")
async def get_node_red_flows_only():
    """
    仅返回Node-RED流程配置数据，不包含元数据。
    适用于直接应用到Node-RED的场景。
    """
    try:
        # 检查配置文件是否存在
        if not os.path.exists(NODE_RED_FLOWS_FILE):
            raise HTTPException(
                status_code=404,
                detail="Node-RED配置文件不存在"
            )
        
        # 读取并解析配置文件
        with open(NODE_RED_FLOWS_FILE, 'r', encoding='utf-8') as f:
            flows_data = json.load(f)
        
        return flows_data
        
    except json.JSONDecodeError as e:
        raise HTTPException(
            status_code=500,
            detail=f"配置文件格式错误: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取Node-RED流程数据失败: {str(e)}"
        )


class NodeRedConfigUpdate(BaseModel):
    flows: list = Field(..., description="Node-RED流程配置数组")
    backup: bool = Field(True, description="是否备份当前配置")
    description: Optional[str] = Field(None, description="更新说明")


@app.post("/node-red-config/update", tags=["Node-RED配置"], summary="更新Node-RED配置")
async def update_node_red_config(
    config: NodeRedConfigUpdate,
    db: Session = Depends(get_db)
):
    """
    更新Node-RED配置文件。
    
    此操作会：
    1. 验证配置格式
    2. 备份当前配置（可选）
    3. 更新配置文件
    4. 记录操作日志
    
    - **flows**: Node-RED流程配置数组
    - **backup**: 是否备份当前配置（默认true）
    - **description**: 更新说明（可选）
    """
    try:
        # 验证flows格式
        if not isinstance(config.flows, list):
            raise HTTPException(
                status_code=400,
                detail="flows必须是数组格式"
            )
        
        # 备份当前配置
        backup_path = None
        if config.backup and os.path.exists(NODE_RED_FLOWS_FILE):
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"flows_backup_{timestamp}.json"
            backup_path = os.path.join(NODE_RED_CONFIG_DIR, backup_filename)
            
            # 创建备份
            with open(NODE_RED_FLOWS_FILE, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
        
        # 保存新配置
        with open(NODE_RED_FLOWS_FILE, 'w', encoding='utf-8') as f:
            json.dump(config.flows, f, ensure_ascii=False, indent=2)
        
        # 记录操作日志
        crud.create_operation_log(
            db=db,
            operation_type="node_red_config_update",
            operation_details=f"更新Node-RED配置: {config.description or '手动更新'}",
            additional_data={
                "flow_count": len(config.flows),
                "backup_created": backup_path is not None,
                "backup_path": backup_path,
                "description": config.description,
                "file_size": os.path.getsize(NODE_RED_FLOWS_FILE)
            }
        )
        
        return {
            "success": True,
            "message": "Node-RED配置更新成功",
            "flow_count": len(config.flows),
            "backup_created": backup_path is not None,
            "backup_path": backup_path,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"更新Node-RED配置失败: {str(e)}"
        )


@app.get("/node-red-config/backups", tags=["Node-RED配置"], summary="获取配置备份列表")
async def get_node_red_config_backups():
    """
    获取所有Node-RED配置备份文件的列表。
    """
    try:
        backups = []
        config_dir = Path(NODE_RED_CONFIG_DIR)
        
        for file_path in config_dir.glob("flows_backup_*.json"):
            try:
                # 从文件名提取时间戳
                filename = file_path.stem
                timestamp_str = filename.replace('flows_backup_', '')
                
                # 获取文件信息
                file_stat = file_path.stat()
                
                backups.append({
                    'filename': file_path.name,
                    'timestamp': timestamp_str,
                    'file_size': file_stat.st_size,
                    'created_time': datetime.datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    'modified_time': datetime.datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                    'file_path': str(file_path)
                })
            except Exception:
                continue  # 跳过解析失败的文件
        
        # 按创建时间排序（最新的在前）
        backups.sort(key=lambda x: x['created_time'], reverse=True)
        
        return {
            "success": True,
            "backups": backups,
            "total_count": len(backups),
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取备份列表失败: {str(e)}"
        )


@app.get("/node-red-config/backup/{filename}", tags=["Node-RED配置"], summary="下载指定备份文件")
async def download_node_red_backup(filename: str):
    """
    下载指定的Node-RED配置备份文件。
    
    - **filename**: 备份文件名（如 "flows_backup_20250725_143022.json"）
    """
    try:
        # 验证文件名格式
        if not filename.startswith('flows_backup_') or not filename.endswith('.json'):
            raise HTTPException(
                status_code=400,
                detail="无效的备份文件名格式"
            )
        
        backup_file = os.path.join(NODE_RED_CONFIG_DIR, filename)
        
        if not os.path.exists(backup_file):
            raise HTTPException(
                status_code=404,
                detail=f"备份文件 {filename} 不存在"
            )
        
        # 读取备份文件
        with open(backup_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
        
        return {
            "success": True,
            "filename": filename,
            "flows": backup_data,
            "flow_count": len(backup_data) if isinstance(backup_data, list) else 0,
            "download_time": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except json.JSONDecodeError as e:
        raise HTTPException(
            status_code=500,
            detail=f"备份文件格式错误: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"下载备份文件失败: {str(e)}"
        )


@app.post("/node-red-config/restore/{filename}", tags=["Node-RED配置"], summary="恢复指定备份配置")
async def restore_node_red_backup(
    filename: str,
    db: Session = Depends(get_db)
):
    """
    从备份文件恢复Node-RED配置。
    
    - **filename**: 要恢复的备份文件名
    """
    try:
        # 验证文件名格式
        if not filename.startswith('flows_backup_') or not filename.endswith('.json'):
            raise HTTPException(
                status_code=400,
                detail="无效的备份文件名格式"
            )
        
        backup_file = os.path.join(NODE_RED_CONFIG_DIR, filename)
        
        if not os.path.exists(backup_file):
            raise HTTPException(
                status_code=404,
                detail=f"备份文件 {filename} 不存在"
            )
        
        # 读取备份文件
        with open(backup_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
        
        # 验证备份数据格式
        if not isinstance(backup_data, list):
            raise HTTPException(
                status_code=400,
                detail="备份文件格式错误：flows必须是数组"
            )
        
        # 备份当前配置
        if os.path.exists(NODE_RED_FLOWS_FILE):
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            current_backup = f"flows_backup_before_restore_{timestamp}.json"
            current_backup_path = os.path.join(NODE_RED_CONFIG_DIR, current_backup)
            
            with open(NODE_RED_FLOWS_FILE, 'r', encoding='utf-8') as src:
                with open(current_backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
        
        # 恢复备份配置
        with open(NODE_RED_FLOWS_FILE, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)
        
        # 记录操作日志
        crud.create_operation_log(
            db=db,
            operation_type="node_red_config_restore",
            operation_details=f"从备份文件恢复Node-RED配置: {filename}",
            additional_data={
                "restored_from": filename,
                "flow_count": len(backup_data),
                "current_backup_created": current_backup_path if os.path.exists(NODE_RED_FLOWS_FILE) else None
            }
        )
        
        return {
            "success": True,
            "message": f"配置已从备份 {filename} 恢复",
            "restored_from": filename,
            "flow_count": len(backup_data),
            "current_backup_created": current_backup_path if 'current_backup_path' in locals() else None,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except json.JSONDecodeError as e:
        raise HTTPException(
            status_code=500,
            detail=f"备份文件格式错误: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"恢复配置失败: {str(e)}"
        )


@app.delete("/node-red-config/backup/{filename}", tags=["Node-RED配置"], summary="删除指定备份文件")
async def delete_node_red_backup(
    filename: str,
    db: Session = Depends(get_db)
):
    """
    删除指定的Node-RED配置备份文件。
    
    - **filename**: 要删除的备份文件名
    """
    try:
        # 验证文件名格式
        if not filename.startswith('flows_backup_') or not filename.endswith('.json'):
            raise HTTPException(
                status_code=400,
                detail="无效的备份文件名格式"
            )
        
        backup_file = os.path.join(NODE_RED_CONFIG_DIR, filename)
        
        if not os.path.exists(backup_file):
            raise HTTPException(
                status_code=404,
                detail=f"备份文件 {filename} 不存在"
            )
        
        # 获取文件信息用于日志
        file_stat = os.stat(backup_file)
        file_size = file_stat.st_size
        
        # 删除备份文件
        os.remove(backup_file)
        
        # 记录操作日志
        crud.create_operation_log(
            db=db,
            operation_type="node_red_backup_delete",
            operation_details=f"删除Node-RED配置备份: {filename}",
            additional_data={
                "deleted_file": filename,
                "file_size": file_size
            }
        )
        
        return {
            "success": True,
            "message": f"备份文件 {filename} 已删除",
            "deleted_file": filename,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除备份文件失败: {str(e)}"
        )


# == Node-RED配置管理接口结束 ==


# == 用电度数统计接口 ==

@app.get("/power-consumption/stats", tags=["用电统计"], summary="获取用电度数统计")
async def get_power_consumption_statistics(
    device_sn: Optional[str] = Query(None, description="设备序列号，为空则统计所有设备")
):
    """
    获取用电度数统计信息。
    
    返回信息包括：
    - 今日用电度数
    - 昨日用电度数  
    - 本月用电度数
    - 上月用电度数
    
    - **device_sn**: （可选）指定设备序列号，为空则统计所有设备
    """
    try:
        stats = power_consumption.get_power_consumption_stats(device_sn)
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取用电统计失败: {str(e)}"
        )


@app.get("/power-consumption/all-devices", tags=["用电统计"], summary="获取所有设备用电统计")
async def get_all_devices_power_consumption():
    """
    获取所有设备的用电度数统计。
    
    返回信息包括：
    - 总体用电统计
    - 各设备分别的用电统计
    - 设备数量统计
    """
    try:
        all_stats = power_consumption.get_all_devices_consumption()
        return all_stats
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取所有设备用电统计失败: {str(e)}"
        )


@app.get("/power-consumption/today", tags=["用电统计"], summary="获取今日用电度数")
async def get_today_power_consumption(
    device_sn: Optional[str] = Query(None, description="设备序列号，为空则统计所有设备")
):
    """
    获取今日用电度数。
    
    - **device_sn**: （可选）指定设备序列号，为空则统计所有设备
    """
    try:
        stats = power_consumption.get_power_consumption_stats(device_sn)
        return {
            "device_sn": device_sn or "all",
            "today_consumption": stats["statistics"]["today"]["consumption"],
            "period": stats["statistics"]["today"]["period"],
            "timestamp": stats["timestamp"]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取今日用电度数失败: {str(e)}"
        )


@app.get("/power-consumption/yesterday", tags=["用电统计"], summary="获取昨日用电度数")
async def get_yesterday_power_consumption(
    device_sn: Optional[str] = Query(None, description="设备序列号，为空则统计所有设备")
):
    """
    获取昨日用电度数。
    
    - **device_sn**: （可选）指定设备序列号，为空则统计所有设备
    """
    try:
        stats = power_consumption.get_power_consumption_stats(device_sn)
        return {
            "device_sn": device_sn or "all",
            "yesterday_consumption": stats["statistics"]["yesterday"]["consumption"],
            "period": stats["statistics"]["yesterday"]["period"],
            "timestamp": stats["timestamp"]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取昨日用电度数失败: {str(e)}"
        )


@app.get("/power-consumption/this-month", tags=["用电统计"], summary="获取本月用电度数")
async def get_this_month_power_consumption(
    device_sn: Optional[str] = Query(None, description="设备序列号，为空则统计所有设备")
):
    """
    获取本月用电度数。
    
    - **device_sn**: （可选）指定设备序列号，为空则统计所有设备
    """
    try:
        stats = power_consumption.get_power_consumption_stats(device_sn)
        return {
            "device_sn": device_sn or "all",
            "this_month_consumption": stats["statistics"]["this_month"]["consumption"],
            "period": stats["statistics"]["this_month"]["period"],
            "timestamp": stats["timestamp"]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取本月用电度数失败: {str(e)}"
        )


@app.get("/power-consumption/last-month", tags=["用电统计"], summary="获取上月用电度数")
async def get_last_month_power_consumption(
    device_sn: Optional[str] = Query(None, description="设备序列号，为空则统计所有设备")
):
    """
    获取上月用电度数。
    
    - **device_sn**: （可选）指定设备序列号，为空则统计所有设备
    """
    try:
        stats = power_consumption.get_power_consumption_stats(device_sn)
        return {
            "device_sn": device_sn or "all",
            "last_month_consumption": stats["statistics"]["last_month"]["consumption"],
            "period": stats["statistics"]["last_month"]["period"],
            "timestamp": stats["timestamp"]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取上月用电度数失败: {str(e)}"
        )


# 定义用电统计响应模型
class PowerConsumptionPeriod(BaseModel):
    consumption: float = Field(description="用电量（度数）")
    period: str = Field(description="统计时间段")

class PowerConsumptionStats(BaseModel):
    device_sn: str = Field(description="设备序列号")
    statistics: dict = Field(description="统计数据")
    timestamp: str = Field(description="统计时间")

# == 用电度数统计接口结束 ==

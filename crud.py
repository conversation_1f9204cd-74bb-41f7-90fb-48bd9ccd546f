# /f:/水利站/backend/crud.py
from sqlalchemy.orm import Session
import models
from typing import Optional


def create_device_data_log(db: Session, sn: str, data: dict):
    """
    在数据库中创建一条新的设备数据日志。
    :param db: 数据库会话对象
    :param sn: 设备序列号
    :param data: 完整的JSON数据字典
    :return: 创建的数据库对象
    """
    # 创建一个DeviceDataLog的ORM实例
    db_log = models.DeviceDataLog(device_sn=sn, raw_data=data)
    # 添加到会话
    db.add(db_log)
    # 提交事务，将数据写入数据库
    db.commit()
    # 刷新实例，以获取数据库生成的值（如ID和默认时间戳）
    db.refresh(db_log)
    return db_log


def get_device_data_logs_by_sn(
    db: Session, 
    sn: Optional[str] = None, 
    skip: int = 0, 
    limit: int = 100,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    field_filters: Optional[dict] = None,
    search_keyword: Optional[str] = None
):
    """
    根据设备SN从数据库中查询历史数据记录，并返回总数和分页结果。
    支持分页查询、时间范围筛选、字段筛选和关键字搜索。
    :param db: 数据库会话对象
    :param sn: 要查询的设备序列号（可选）
    :param skip: 跳过的记录数（用于分页）
    :param limit: 返回的最大记录数（用于分页）
    :param start_time: 开始时间（ISO格式字符串）
    :param end_time: 结束时间（ISO格式字符串）
    :param field_filters: 字段筛选条件字典，如 {"float1": 1, "water_pump1.status": 1}
    :param search_keyword: 搜索关键字（在raw_data中搜索）
    :return: 包含总数和数据列表的字典
    """
    from datetime import datetime
    import json
    
    # 构建基础查询
    query = db.query(models.DeviceDataLog)
    
    # 设备SN筛选
    if sn:
        query = query.filter(models.DeviceDataLog.device_sn == sn)
    
    # 时间范围筛选
    if start_time:
        try:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            query = query.filter(models.DeviceDataLog.timestamp >= start_dt)
        except ValueError:
            pass  # 忽略无效的时间格式
    
    if end_time:
        try:
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            query = query.filter(models.DeviceDataLog.timestamp <= end_dt)
        except ValueError:
            pass  # 忽略无效的时间格式
    
    # 字段筛选
    if field_filters:
        for field_path, value in field_filters.items():
            if '.' in field_path:
                # 嵌套字段筛选，如 "water_pump1.status"
                field_parts = field_path.split('.')
                json_path = f"$.{'.'.join(field_parts)}"
                query = query.filter(
                    models.DeviceDataLog.raw_data.op('JSON_EXTRACT')(json_path) == value
                )
            else:
                # 顶级字段筛选
                query = query.filter(
                    models.DeviceDataLog.raw_data.op('JSON_EXTRACT')(f'$.{field_path}') == value
                )
    
    # 关键字搜索
    if search_keyword:
        query = query.filter(
            models.DeviceDataLog.raw_data.op('LIKE')(f'%{search_keyword}%')
        )
    
    # 第一次查询：获取总记录数
    total = query.count()

    # 第二次查询：获取分页数据
    items = (
        query
        .order_by(models.DeviceDataLog.timestamp.desc())  # 按时间倒序排序
        .offset(skip)
        .limit(limit)
        .all()
    )
    return {"total": total, "items": items}


# -- 新增任务元数据操作 --


def create_task_metadata(db: Session, task_id: str, task_type: str, metadata: dict):
    """
    创建一条任务元数据记录。
    """
    db_task = models.TaskMetadata(
        task_id=task_id, task_type=task_type, task_data=metadata
    )
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    return db_task


def get_all_task_metadata(db: Session):
    """
    获取所有任务元数据记录。
    """
    return db.query(models.TaskMetadata).all()


def delete_task_metadata(db: Session, task_id: str):
    """
    根据任务ID删除一条元数据记录。
    """
    db_task = (
        db.query(models.TaskMetadata)
        .filter(models.TaskMetadata.task_id == task_id)
        .first()
    )
    if db_task:
        db.delete(db_task)
        db.commit()
        return True
    return False


# -- 新增: 系统键值对存储操作 --


def get_kv(db: Session, key: str) -> Optional[str]:
    """
    从数据库中获取一个键值对的值。
    """
    record = (
        db.query(models.SystemKVStore).filter(models.SystemKVStore.key == key).first()
    )
    return record.value if record else None


def set_kv(db: Session, key: str, value: str):
    """
    在数据库中设置一个键值对的值 (如果存在则更新, 不存在则创建)。
    """
    record = (
        db.query(models.SystemKVStore).filter(models.SystemKVStore.key == key).first()
    )
    if record:
        record.value = value
    else:
        record = models.SystemKVStore(key=key, value=value)
        db.add(record)
    db.commit()
    return record


# -- 新增: 操作日志记录相关函数 --


def create_operation_log(
    db: Session,
    operation_type: str,
    operation_details: str,
    execution_status: str = "pending",
    device_sn: Optional[str] = None,
    command_sent: Optional[str] = None,
    error_message: Optional[str] = None,
    additional_data: Optional[dict] = None,
):
    """
    创建一条操作日志记录。
    :param db: 数据库会话对象
    :param operation_type: 操作类型
    :param operation_details: 操作详细描述
    :param execution_status: 执行状态
    :param device_sn: 相关设备序列号
    :param command_sent: 发送的命令内容
    :param error_message: 错误信息
    :param additional_data: 额外的上下文数据
    :return: 创建的操作日志对象
    """
    db_log = models.OperationLog(
        operation_type=operation_type,
        device_sn=device_sn,
        operation_details=operation_details,
        command_sent=command_sent,
        execution_status=execution_status,
        error_message=error_message,
        additional_data=additional_data,
    )
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log


def update_operation_log_status(
    db: Session, log_id: int, execution_status: str, error_message: Optional[str] = None
):
    """
    更新操作日志的执行状态。
    :param db: 数据库会话对象
    :param log_id: 日志记录ID
    :param execution_status: 新的执行状态
    :param error_message: 错误信息（如果有）
    :return: 更新后的操作日志对象
    """
    db_log = db.query(models.OperationLog).filter(models.OperationLog.id == log_id).first()
    if db_log:
        db_log.execution_status = execution_status
        if error_message:
            db_log.error_message = error_message
        db.commit()
        db.refresh(db_log)
    return db_log


def get_operation_logs(
    db: Session,
    operation_type: Optional[str] = None,
    device_sn: Optional[str] = None,
    execution_status: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    search_keyword: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
):
    """
    查询操作日志记录，支持多种筛选条件。
    :param db: 数据库会话对象
    :param operation_type: 过滤操作类型
    :param device_sn: 过滤设备序列号
    :param execution_status: 过滤执行状态
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param search_keyword: 搜索关键字
    :param skip: 跳过的记录数
    :param limit: 返回的最大记录数
    :return: 包含总数和数据列表的字典
    """
    from datetime import datetime
    
    query = db.query(models.OperationLog)
    
    # 操作类型筛选
    if operation_type:
        query = query.filter(models.OperationLog.operation_type == operation_type)
    
    # 设备SN筛选
    if device_sn:
        query = query.filter(models.OperationLog.device_sn == device_sn)
    
    # 执行状态筛选
    if execution_status:
        query = query.filter(models.OperationLog.execution_status == execution_status)
    
    # 时间范围筛选
    if start_time:
        try:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            query = query.filter(models.OperationLog.timestamp >= start_dt)
        except ValueError:
            pass  # 忽略无效的时间格式
    
    if end_time:
        try:
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            query = query.filter(models.OperationLog.timestamp <= end_dt)
        except ValueError:
            pass  # 忽略无效的时间格式
    
    # 关键字搜索
    if search_keyword:
        query = query.filter(
            models.OperationLog.operation_details.contains(search_keyword) |
            models.OperationLog.command_sent.contains(search_keyword) |
            models.OperationLog.error_message.contains(search_keyword)
        )
    
    total = query.count()
    items = (
        query.order_by(models.OperationLog.timestamp.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )
    
    return {"total": total, "items": items}

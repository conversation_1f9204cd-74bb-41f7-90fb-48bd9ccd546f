# 用电度数统计模块
import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, extract
from database import SessionLocal
from models import DeviceDataLog


def get_energy_data_from_raw(raw_data: dict) -> float:
    """
    从设备原始数据中提取正向有功总电能数据

    Args:
        raw_data: 设备原始JSON数据

    Returns:
        float: 正向有功总电能（度数）
    """
    try:
        diannengbiao = raw_data.get("diannengbiao", {})
        active_energy = diannengbiao.get("active_energy", 0)
        return float(active_energy) if active_energy is not None else 0.0
    except (ValueError, TypeError):
        return 0.0


def get_reverse_energy_data_from_raw(raw_data: dict) -> float:
    """
    从设备原始数据中提取反向有功总电能数据

    Args:
        raw_data: 设备原始JSON数据

    Returns:
        float: 反向有功总电能（度数）
    """
    try:
        diannengbiao = raw_data.get("diannengbiao", {})
        reverse_active_energy = diannengbiao.get("reverse_active_energy", 0)
        return float(reverse_active_energy) if reverse_active_energy is not None else 0.0
    except (ValueError, TypeError):
        return 0.0


def get_power_consumption_stats(device_sn: str = None) -> dict:
    """
    获取用电度数统计信息
    
    Args:
        device_sn: 设备序列号，如果为None则统计所有设备
    
    Returns:
        dict: 包含今日、昨日、本月、上月用电度数的统计
    """
    db = SessionLocal()
    try:
        # 获取当前时间
        now = datetime.datetime.now()
        
        # 计算时间边界
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_start = today_start - datetime.timedelta(days=1)
        
        # 本月开始时间
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # 上月开始和结束时间
        if now.month == 1:
            last_month_start = now.replace(year=now.year-1, month=12, day=1, hour=0, minute=0, second=0, microsecond=0)
        else:
            last_month_start = now.replace(month=now.month-1, day=1, hour=0, minute=0, second=0, microsecond=0)
        
        last_month_end = month_start
        
        # 构建基础查询条件
        base_query = db.query(DeviceDataLog)
        if device_sn:
            base_query = base_query.filter(DeviceDataLog.device_sn == device_sn)
        
        # 获取今日用电度数
        today_power = calculate_period_consumption(
            db, base_query, today_start, now, device_sn
        )
        
        # 获取昨日用电度数
        yesterday_power = calculate_period_consumption(
            db, base_query, yesterday_start, today_start, device_sn
        )
        
        # 获取本月用电度数
        month_power = calculate_period_consumption(
            db, base_query, month_start, now, device_sn
        )
        
        # 获取上月用电度数
        last_month_power = calculate_period_consumption(
            db, base_query, last_month_start, last_month_end, device_sn
        )
        
        return {
            "device_sn": device_sn or "all",
            "statistics": {
                "today": {
                    "consumption": round(today_power, 2),
                    "period": f"{today_start.strftime('%Y-%m-%d %H:%M:%S')} ~ {now.strftime('%Y-%m-%d %H:%M:%S')}"
                },
                "yesterday": {
                    "consumption": round(yesterday_power, 2),
                    "period": f"{yesterday_start.strftime('%Y-%m-%d %H:%M:%S')} ~ {today_start.strftime('%Y-%m-%d %H:%M:%S')}"
                },
                "this_month": {
                    "consumption": round(month_power, 2),
                    "period": f"{month_start.strftime('%Y-%m-%d %H:%M:%S')} ~ {now.strftime('%Y-%m-%d %H:%M:%S')}"
                },
                "last_month": {
                    "consumption": round(last_month_power, 2),
                    "period": f"{last_month_start.strftime('%Y-%m-%d %H:%M:%S')} ~ {last_month_end.strftime('%Y-%m-%d %H:%M:%S')}"
                }
            },
            "timestamp": now.strftime('%Y-%m-%d %H:%M:%S')
        }
        
    finally:
        db.close()


def get_energy_data_for_device(raw_data: dict, device_sn: str = None) -> float:
    """
    根据设备SN获取对应的用电量数据

    Args:
        raw_data: 设备原始JSON数据
        device_sn: 设备序列号

    Returns:
        float: 用电量数据（度数）
    """
    # 特殊处理：大船港村曹村的特定点位使用反向有功总电能
    if device_sn == "02800125071500004977":
        return get_reverse_energy_data_from_raw(raw_data)
    else:
        # 其他设备使用正向有功总电能
        return get_energy_data_from_raw(raw_data)


def find_first_valid_energy_record(db: Session, base_query, start_time: datetime.datetime,
                                  end_time: datetime.datetime, device_sn: str = None):
    """
    查找第一条有效的用电量数据记录

    对于特殊设备02800125071500004977，查找第一条有reverse_active_energy数据的记录
    对于其他设备，查找第一条有active_energy数据的记录

    Args:
        db: 数据库会话
        base_query: 基础查询对象
        start_time: 开始时间
        end_time: 结束时间
        device_sn: 设备序列号

    Returns:
        DeviceDataLog: 第一条有效记录，如果没有则返回None
    """
    try:
        # 构建时间过滤条件
        time_filter = and_(
            DeviceDataLog.timestamp >= start_time,
            DeviceDataLog.timestamp < end_time
        )

        # 获取时间段内所有记录，按时间升序排列
        records = base_query.filter(time_filter).order_by(DeviceDataLog.timestamp.asc()).all()

        if not records:
            return None

        # 对于特殊设备，查找第一条有reverse_active_energy数据的记录
        if device_sn == "02800125071500004977":
            for record in records:
                reverse_energy = get_reverse_energy_data_from_raw(record.raw_data)
                if reverse_energy > 0:  # 有有效的反向有功总电能数据
                    return record
        else:
            # 对于其他设备，查找第一条有active_energy数据的记录
            for record in records:
                active_energy = get_energy_data_from_raw(record.raw_data)
                if active_energy > 0:  # 有有效的正向有功总电能数据
                    return record

        # 如果没有找到有效数据，返回第一条记录
        return records[0] if records else None

    except Exception as e:
        print(f"查找第一条有效用电量记录时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def calculate_period_consumption(db: Session, base_query, start_time: datetime.datetime,
                               end_time: datetime.datetime, device_sn: str = None) -> float:
    """
    计算指定时间段内的用电量消耗

    Args:
        db: 数据库会话
        base_query: 基础查询对象
        start_time: 开始时间
        end_time: 结束时间
        device_sn: 设备序列号

    Returns:
        float: 时间段内的用电量消耗
    """
    try:
        # 构建时间过滤条件
        time_filter = and_(
            DeviceDataLog.timestamp >= start_time,
            DeviceDataLog.timestamp < end_time
        )

        # 对于特殊设备，使用特殊的基准查找逻辑
        if device_sn == "02800125071500004977":
            first_record = find_first_valid_energy_record(db, base_query, start_time, end_time, device_sn)
        else:
            # 其他设备使用原有逻辑
            first_record = base_query.filter(time_filter).order_by(DeviceDataLog.timestamp.asc()).first()

        # 获取最后一条记录
        last_record = base_query.filter(time_filter).order_by(DeviceDataLog.timestamp.desc()).first()

        if not first_record or not last_record:
            return 0.0

        # 如果是同一条记录，说明时间段内只有一条数据，消耗为0
        if first_record.id == last_record.id:
            return 0.0

        # 根据设备SN提取对应的用电量数据
        first_energy = get_energy_data_for_device(first_record.raw_data, device_sn)
        last_energy = get_energy_data_for_device(last_record.raw_data, device_sn)

        # 计算消耗量（后减前）
        consumption = last_energy - first_energy

        # 如果消耗量为负数，可能是电表重置，这种情况返回最后记录的值
        if consumption < 0:
            consumption = last_energy

        return max(0.0, consumption)

    except Exception as e:
        print(f"计算时间段用电消耗时出错: {e}")
        import traceback
        traceback.print_exc()
        return 0.0


def get_all_devices_consumption() -> dict:
    """
    获取所有设备的用电统计
    
    Returns:
        dict: 包含所有设备用电统计的字典
    """
    db = SessionLocal()
    try:
        # 获取所有设备列表
        devices = db.query(DeviceDataLog.device_sn).distinct().all()
        device_list = [device[0] for device in devices]
        
        # 总体统计
        total_stats = get_power_consumption_stats()
        
        # 各设备统计
        device_stats = {}
        for device_sn in device_list:
            device_stats[device_sn] = get_power_consumption_stats(device_sn)
        
        return {
            "total": total_stats,
            "devices": device_stats,
            "device_count": len(device_list)
        }
        
    finally:
        db.close()
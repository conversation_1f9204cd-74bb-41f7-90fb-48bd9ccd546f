# /f:/水利站/backend/device_timing.py
import time
import threading
import datetime
from typing import Callable, Any
from database import SessionLocal
import crud

# 设备控制延时配置
DEVICE_TIMING_CONFIG = {
    # 继电器响应延时（秒）
    "relay_response_delay": 2.0,
    # 水泵停止后的等待时间（秒）
    "pump_stop_delay": 5.0,
    # 水泵启动前的等待时间（秒）
    "pump_start_delay": 3.0,
    # 同一设备命令间隔（秒）
    "command_interval": 1.0,
    # 强制轮换时的延时（秒）
    "force_switch_delay": 8.0,
}

# 设备最后命令时间记录
_device_last_command_time = {}
_command_time_lock = threading.Lock()


def get_device_last_command_time(sn: str) -> float:
    """获取设备最后一次命令时间"""
    with _command_time_lock:
        return _device_last_command_time.get(sn, 0.0)


def set_device_last_command_time(sn: str, timestamp: float):
    """设置设备最后一次命令时间"""
    with _command_time_lock:
        _device_last_command_time[sn] = timestamp


def wait_for_device_ready(sn: str, min_interval: float = None):
    """
    等待设备准备就绪（距离上次命令足够时间间隔）
    
    :param sn: 设备序列号
    :param min_interval: 最小间隔时间，如果为None则使用默认配置
    """
    if min_interval is None:
        min_interval = DEVICE_TIMING_CONFIG["command_interval"]
    
    last_time = get_device_last_command_time(sn)
    current_time = time.time()
    elapsed = current_time - last_time
    
    if elapsed < min_interval:
        wait_time = min_interval - elapsed
        current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time_str}] [timing] 设备 {sn} 等待 {wait_time:.1f}秒 后再发送命令")
        time.sleep(wait_time)


def delayed_command_execution(
    sn: str, 
    delay_seconds: float, 
    command_func: Callable,
    command_args: tuple = (),
    command_kwargs: dict = None,
    operation_description: str = "",
    related_log_id: int = None
):
    """
    延时执行设备命令
    
    :param sn: 设备序列号
    :param delay_seconds: 延时秒数
    :param command_func: 要执行的命令函数
    :param command_args: 命令函数的位置参数
    :param command_kwargs: 命令函数的关键字参数
    :param operation_description: 操作描述
    :param related_log_id: 关联的日志ID
    """
    if command_kwargs is None:
        command_kwargs = {}
    
    def delayed_task():
        current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 记录延时开始
        db = SessionLocal()
        try:
            delay_log = crud.create_operation_log(
                db=db,
                operation_type="delayed_command",
                operation_details=f"延时 {delay_seconds}秒 后执行: {operation_description}",
                device_sn=sn,
                execution_status="pending",
                additional_data={
                    "delay_seconds": delay_seconds,
                    "related_log_id": related_log_id,
                    "start_time": current_time_str
                }
            )
        except Exception as e:
            print(f"[{current_time_str}] [timing] 创建延时日志失败: {e}")
            delay_log = None
        finally:
            db.close()
        
        print(f"[{current_time_str}] [timing] 开始延时 {delay_seconds}秒: {operation_description}")
        time.sleep(delay_seconds)
        
        # 执行命令
        execution_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{execution_time_str}] [timing] 延时完成，执行命令: {operation_description}")
        
        try:
            # 等待设备准备就绪
            wait_for_device_ready(sn)
            
            # 执行命令
            result = command_func(*command_args, **command_kwargs)
            
            # 更新设备最后命令时间
            set_device_last_command_time(sn, time.time())
            
            # 更新延时日志状态
            if delay_log:
                db = SessionLocal()
                try:
                    success = result[0] if isinstance(result, tuple) else result
                    status = "success" if success else "failed"
                    crud.update_operation_log_status(
                        db, delay_log.id, status, 
                        None if success else "命令执行失败"
                    )
                finally:
                    db.close()
            
            return result
            
        except Exception as e:
            error_msg = f"延时命令执行失败: {e}"
            print(f"[{execution_time_str}] [timing] {error_msg}")
            
            # 更新延时日志状态
            if delay_log:
                db = SessionLocal()
                try:
                    crud.update_operation_log_status(db, delay_log.id, "failed", error_msg)
                finally:
                    db.close()
            
            return False, None
    
    # 在后台线程中执行延时任务
    delay_thread = threading.Thread(target=delayed_task, daemon=True)
    delay_thread.start()
    
    return delay_thread


def create_pump_sequence_with_delay(
    sn: str,
    stop_pump: str,
    start_pump: str,
    stop_log_id: int = None,
    start_log_id: int = None
):
    """
    创建带延时的水泵切换序列
    
    :param sn: 设备序列号
    :param stop_pump: 要停止的水泵名称
    :param start_pump: 要启动的水泵名称
    :param stop_log_id: 停止操作的日志ID
    :param start_log_id: 启动操作的日志ID
    """
    from device_control import create_do_command, send_command_to_device
    
    # 水泵DO映射
    PUMP_TO_DO_MAPPING = {
        "water_pump1": "DO21",
        "water_pump2": "DO22",
        "air_pump1": "DO23",
        "air_pump2": "DO24",
    }
    
    current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time_str}] [timing] 开始水泵切换序列: {stop_pump} -> {start_pump}")
    
    # 第一步：立即停止当前水泵
    if stop_pump:
        stop_do = PUMP_TO_DO_MAPPING.get(stop_pump)
        if stop_do:
            stop_command = create_do_command(do_name=stop_do, value=0)
            send_command_to_device(sn=sn, command=stop_command)
            print(f"[{current_time_str}] [timing] 立即停止水泵 {stop_pump}")
    
    # 第二步：延时后启动新水泵
    if start_pump:
        start_do = PUMP_TO_DO_MAPPING.get(start_pump)
        if start_do:
            delay_seconds = DEVICE_TIMING_CONFIG["pump_stop_delay"]
            
            def start_pump_command():
                start_command = create_do_command(do_name=start_do, value=1)
                return send_command_to_device(sn=sn, command=start_command)
            
            delayed_command_execution(
                sn=sn,
                delay_seconds=delay_seconds,
                command_func=start_pump_command,
                operation_description=f"启动水泵 {start_pump} (DO: {start_do})",
                related_log_id=start_log_id
            )
            
            print(f"[{current_time_str}] [timing] 已安排 {delay_seconds}秒后启动水泵 {start_pump}")


def create_delayed_pump_stop(sn: str, pump_name: str, delay_seconds: float = None, related_log_id: int = None):
    """
    创建延时停止水泵的任务
    
    :param sn: 设备序列号
    :param pump_name: 水泵名称
    :param delay_seconds: 延时秒数，如果为None则使用默认配置
    :param related_log_id: 关联的日志ID
    """
    from device_control import create_do_command, send_command_to_device
    
    if delay_seconds is None:
        delay_seconds = DEVICE_TIMING_CONFIG["pump_stop_delay"]
    
    PUMP_TO_DO_MAPPING = {
        "water_pump1": "DO21",
        "water_pump2": "DO22",
        "air_pump1": "DO23",
        "air_pump2": "DO24",
    }
    
    pump_do = PUMP_TO_DO_MAPPING.get(pump_name)
    if not pump_do:
        print(f"[timing] 无效的水泵名称: {pump_name}")
        return None
    
    def stop_pump_command():
        stop_command = create_do_command(do_name=pump_do, value=0)
        return send_command_to_device(sn=sn, command=stop_command)
    
    return delayed_command_execution(
        sn=sn,
        delay_seconds=delay_seconds,
        command_func=stop_pump_command,
        operation_description=f"停止水泵 {pump_name} (DO: {pump_do})",
        related_log_id=related_log_id
    )
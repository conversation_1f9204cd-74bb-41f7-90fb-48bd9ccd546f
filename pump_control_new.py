# /f:/水利站/backend/pump_control_new.py
"""
新的水泵和气泵控制逻辑：
1. 水泵交替启动：浮球激活时交替启动水泵1和水泵2，浮球复位时关闭当前运行的水泵
2. 气泵故障检测和切换：当一个气泵故障时，另一个气泵持续运行
3. 水泵故障检测和切换：当一个水泵故障时，另一个水泵接管工作
"""
import datetime
from sqlalchemy.orm import Session
import crud
from data_store import DATA_LOCK, DEVICE_DATA
from device_control import create_do_command, send_command_to_device
from device_timing import wait_for_device_ready


def handle_new_pump_logic(db: Session, sn: str, new_data: dict):
    """
    新的水泵和气泵控制逻辑
    """
    # 处理水泵逻辑
    handle_water_pump_logic(db, sn, new_data)
    
    # 处理气泵逻辑
    handle_air_pump_logic(db, sn, new_data)


def handle_water_pump_logic(db: Session, sn: str, new_data: dict):
    """
    水泵三档位控制逻辑
    支持：停止档位 > 手动档位 > 自动档位（浮球驱动的交替启动）
    """
    current_time = datetime.datetime.now()
    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 检查水泵档位状态（停止、手动、自动）
    # 从设备数据结构中读取档位状态
    water_pump1_stop = new_data.get("water_pump1", {}).get("stop_status", 0) == 1
    water_pump2_stop = new_data.get("water_pump2", {}).get("stop_status", 0) == 1
    water_pump1_manual = new_data.get("water_pump1", {}).get("manual_status", 0) == 1
    water_pump2_manual = new_data.get("water_pump2", {}).get("manual_status", 0) == 1
    water_pump1_auto = new_data.get("water_pump1", {}).get("auto_status", 0) == 1
    water_pump2_auto = new_data.get("water_pump2", {}).get("auto_status", 0) == 1
    
    # 获取当前运行状态
    do21_status = new_data.get("DO21_status", 0)
    do22_status = new_data.get("DO22_status", 0)
    
    print(f"[{current_time_str}] [water_pump_logic] 档位状态检查:")
    print(f"  水泵1: 停止={water_pump1_stop}, 手动={water_pump1_manual}, 自动={water_pump1_auto}, 运行={do21_status}")
    print(f"  水泵2: 停止={water_pump2_stop}, 手动={water_pump2_manual}, 自动={water_pump2_auto}, 运行={do22_status}")
    
    # 处理水泵1的三档位控制
    handle_single_water_pump_control(db, sn, "water_pump1", "DO21",
                                     water_pump1_stop, water_pump1_manual, water_pump1_auto,
                                     do21_status, current_time_str, new_data)
    
    # 处理水泵2的三档位控制
    handle_single_water_pump_control(db, sn, "water_pump2", "DO22",
                                     water_pump2_stop, water_pump2_manual, water_pump2_auto,
                                     do22_status, current_time_str, new_data)
    
    # 只有在自动档位时才执行浮球驱动的交替启动逻辑
    if (water_pump1_auto or water_pump2_auto) and not (water_pump1_stop or water_pump2_stop or water_pump1_manual or water_pump2_manual):
        handle_float_driven_alternating_logic(db, sn, new_data, current_time_str)


def handle_single_water_pump_control(db: Session, sn: str, pump_name: str, do_name: str,
                                     stop_active: bool, manual_active: bool, auto_active: bool,
                                     current_status: int, current_time_str: str, device_data: dict):
    """
    处理单个水泵的三档位控制逻辑
    优先级：停止 > 手动 > 自动 > 无档位
    """
    
    # 优先级1：停止档位（最高优先级）
    if stop_active:
        if current_status == 1:
            print(f"[{current_time_str}] [water_pump_logic] {pump_name} 停止档位激活，强制停止")
            stop_water_pump(db, sn, pump_name, do_name, "停止档位激活")
        return
    
    # 优先级2：手动档位（中等优先级）
    elif manual_active:
        if current_status == 0:
            print(f"[{current_time_str}] [water_pump_logic] {pump_name} 手动档位激活，强制启动")
            start_water_pump(db, sn, pump_name, do_name, "手动档位激活")
        else:
            print(f"[{current_time_str}] [water_pump_logic] {pump_name} 手动档位激活，保持运行")
        return
    
    # 优先级3：自动档位（低优先级，交替启动逻辑处理）
    elif auto_active:
        # 自动档位的具体逻辑在handle_float_driven_alternating_logic中处理
        # 这里只做故障检测
        pump_faulty = is_pump_faulty(device_data, pump_name)
        if pump_faulty and current_status == 1:
            print(f"[{current_time_str}] [water_pump_logic] {pump_name} 自动档位检测到故障，停止运行")
            stop_water_pump(db, sn, pump_name, do_name, "自动档位故障停止")
        return
    
    # 优先级4：无档位激活（最低优先级）
    else:
        if current_status == 1:
            print(f"[{current_time_str}] [water_pump_logic] {pump_name} 无档位激活，停止运行")
            stop_water_pump(db, sn, pump_name, do_name, "无档位激活")


def handle_float_driven_alternating_logic(db: Session, sn: str, new_data: dict, current_time_str: str):
    """
    处理浮球驱动的交替启动逻辑（只在自动档位时执行）
    逻辑：
    1. 浮球激活时，交替启动水泵1或水泵2（每次只启动一个）
    2. 浮球复位时，停止当前运行的水泵
    3. 下次浮球激活时，启动另一个水泵（实现交替）
    """
    if "float_switches" not in new_data or "float1" not in new_data["float_switches"]:
        return
        
    current_float1 = new_data["float_switches"]["float1"]
    
    with DATA_LOCK:
        previous_float1 = (
            DEVICE_DATA.get(sn, {}).get("float_switches", {}).get("float1", None)
        )
    
    current_time = datetime.datetime.now()
    
    # 定义用于数据库状态存储的键
    next_pump_key = f"next_pump_to_use_{sn}"  # 下次要使用的水泵
    active_pump_key = f"active_pump_{sn}"     # 当前活跃的水泵
    start_time_key = f"pump_start_time_{sn}"  # 当前水泵启动时间
    
    # Case 1: 浮球复位 (1 -> 0). 关闭当前运行的水泵
    if current_float1 == 0 and previous_float1 == 1:
        active_pump = crud.get_kv(db, active_pump_key)
        print(f"[{current_time_str}] [water_pump_logic] 浮球 '{sn}' 复位。正在关闭当前运行的水泵: {active_pump}")
        
        # 记录操作日志
        pump_log = crud.create_operation_log(
            db=db,
            operation_type="pump_control",
            operation_details=f"浮球复位，关闭水泵 {active_pump}",
            device_sn=sn,
            additional_data={
                "trigger": "float_reset",
                "float1_status": current_float1,
                "active_pump": active_pump
            }
        )
        
        # 检测故障并关闭运行的水泵
        if active_pump == "water_pump1" and new_data.get("DO21_status") == 1:
            # 检查水泵1是否有故障
            if is_pump_faulty(new_data, "water_pump1"):
                print(f"[{current_time_str}] [water_pump_logic] 检测到水泵1故障，下次将使用水泵2")
                # 切换到水泵2并标记为下次使用的泵
                crud.set_kv(db, next_pump_key, "water_pump2")
            
            wait_for_device_ready(sn)
            command = create_do_command(do_name="DO21", value=0)
            success, _ = send_command_to_device(sn=sn, command=command)
            print(f"[{current_time_str}] [water_pump_logic] 水泵1停止指令发送{'成功' if success else '失败'}")
            
        elif active_pump == "water_pump2" and new_data.get("DO22_status") == 1:
            # 检查水泵2是否有故障
            if is_pump_faulty(new_data, "water_pump2"):
                print(f"[{current_time_str}] [water_pump_logic] 检测到水泵2故障，下次将使用水泵1")
                # 切换到水泵1并标记为下次使用的泵
                crud.set_kv(db, next_pump_key, "water_pump1")
            
            wait_for_device_ready(sn)
            command = create_do_command(do_name="DO22", value=0)
            success, _ = send_command_to_device(sn=sn, command=command)
            print(f"[{current_time_str}] [water_pump_logic] 水泵2停止指令发送{'成功' if success else '失败'}")
        
        # 清理当前运行状态
        crud.set_kv(db, active_pump_key, "")
        crud.set_kv(db, start_time_key, "")
        crud.update_operation_log_status(db, pump_log.id, "success")
        return
    
    # Case 2: 浮球首次激活 (0 -> 1)，启动下一个要使用的水泵
    if current_float1 == 1 and previous_float1 in [0, None]:
        # 获取下次要使用的水泵，默认从水泵1开始
        next_pump = crud.get_kv(db, next_pump_key) or "water_pump1"
        
        # 检查目标水泵是否可用（无故障且处于自动模式）
        if next_pump == "water_pump1":
            if is_pump_faulty(new_data, "water_pump1") or new_data.get("water_pump1", {}).get("auto_status") != 1:
                next_pump = "water_pump2"
                print(f"[{current_time_str}] [water_pump_logic] 水泵1不可用，切换到水泵2")
        
        if next_pump == "water_pump2":
            if is_pump_faulty(new_data, "water_pump2") or new_data.get("water_pump2", {}).get("auto_status") != 1:
                next_pump = "water_pump1"
                print(f"[{current_time_str}] [water_pump_logic] 水泵2不可用，切换到水泵1")
        
        # 最终检查：如果目标泵仍不可用，尝试另一个
        final_pump_available = False
        do_name = ""
        
        if next_pump == "water_pump1":
            if not is_pump_faulty(new_data, "water_pump1") and new_data.get("water_pump1", {}).get("auto_status") == 1:
                final_pump_available = True
                do_name = "DO21"
        elif next_pump == "water_pump2":
            if not is_pump_faulty(new_data, "water_pump2") and new_data.get("water_pump2", {}).get("auto_status") == 1:
                final_pump_available = True
                do_name = "DO22"
        
        if not final_pump_available:
            print(f"[{current_time_str}] [water_pump_logic] 两个水泵都不可用，无法启动")
            return
        
        print(f"[{current_time_str}] [water_pump_logic] 浮球 '{sn}' 激活。启动 {next_pump}")
        
        # 记录操作日志
        pump_log = crud.create_operation_log(
            db=db,
            operation_type="pump_control",
            operation_details=f"浮球激活，交替启动 {next_pump} ({do_name})",
            device_sn=sn,
            additional_data={
                "pump_name": next_pump,
                "do_name": do_name,
                "trigger": "float_activation_alternate",
                "float1_status": current_float1
            }
        )
        
        # 启动目标水泵
        wait_for_device_ready(sn)
        command = create_do_command(do_name=do_name, value=1)
        success, _ = send_command_to_device(sn=sn, command=command)
        
        if success:
            # 记录状态到数据库
            crud.set_kv(db, active_pump_key, next_pump)
            crud.set_kv(db, start_time_key, current_time.isoformat())
            
            # 设置下次使用的水泵（交替）
            next_next_pump = "water_pump2" if next_pump == "water_pump1" else "water_pump1"
            crud.set_kv(db, next_pump_key, next_next_pump)
            
            crud.update_operation_log_status(db, pump_log.id, "success")
            print(f"[{current_time_str}] [water_pump_logic] {next_pump}启动成功，下次将使用{next_next_pump}")
        else:
            crud.update_operation_log_status(db, pump_log.id, "failed", "设备命令发送失败")
            print(f"[{current_time_str}] [water_pump_logic] {next_pump}启动失败")
        return


def start_water_pump(db: Session, sn: str, pump_name: str, do_name: str, reason: str):
    """
    启动指定的水泵
    """
    current_time = datetime.datetime.now()
    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 记录操作日志
    pump_log = crud.create_operation_log(
        db=db,
        operation_type="water_pump_control",
        operation_details=f"{reason}，启动 {pump_name} ({do_name})",
        device_sn=sn,
        additional_data={
            "pump_name": pump_name,
            "do_name": do_name,
            "trigger": reason
        }
    )
    
    # 启动水泵
    wait_for_device_ready(sn)
    command = create_do_command(do_name=do_name, value=1)
    success, _ = send_command_to_device(sn=sn, command=command)
    
    if success:
        # 更新状态（手动档位时也需要记录）
        if "手动" in reason:
            active_pump_key = f"active_pump_{sn}"
            start_time_key = f"pump_start_time_{sn}"
            crud.set_kv(db, active_pump_key, pump_name)
            crud.set_kv(db, start_time_key, current_time.isoformat())
        
        crud.update_operation_log_status(db, pump_log.id, "success")
        print(f"[{current_time_str}] [water_pump_logic] {pump_name}启动成功")
    else:
        crud.update_operation_log_status(db, pump_log.id, "failed", "设备命令发送失败")
        print(f"[{current_time_str}] [water_pump_logic] {pump_name}启动失败")


def stop_water_pump(db: Session, sn: str, pump_name: str, do_name: str, reason: str):
    """
    停止指定的水泵
    """
    current_time = datetime.datetime.now()
    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 记录操作日志
    pump_log = crud.create_operation_log(
        db=db,
        operation_type="water_pump_control",
        operation_details=f"{reason}，停止 {pump_name} ({do_name})",
        device_sn=sn,
        additional_data={
            "pump_name": pump_name,
            "do_name": do_name,
            "trigger": reason
        }
    )
    
    # 停止水泵
    wait_for_device_ready(sn)
    command = create_do_command(do_name=do_name, value=0)
    success, _ = send_command_to_device(sn=sn, command=command)
    
    if success:
        # 清理状态（如果停止的是当前活跃的水泵）
        active_pump = crud.get_kv(db, f"active_pump_{sn}")
        if active_pump == pump_name:
            crud.set_kv(db, f"active_pump_{sn}", "")
            crud.set_kv(db, f"pump_start_time_{sn}", "")
        
        crud.update_operation_log_status(db, pump_log.id, "success")
        print(f"[{current_time_str}] [water_pump_logic] {pump_name}停止成功")
    else:
        crud.update_operation_log_status(db, pump_log.id, "failed", "设备命令发送失败")
        print(f"[{current_time_str}] [water_pump_logic] {pump_name}停止失败")


def handle_air_pump_logic(db: Session, sn: str, new_data: dict):
    """
    气泵故障检测和切换逻辑
    支持三档位控制：停止档位 > 手动档位 > 自动档位
    明确策略：
    1. 停止档位：强制停止对应气泵
    2. 手动档位：强制启动对应气泵  
    3. 自动档位：故障检测和智能切换（互斥运行）
    4. 无档位：停止对应气泵
    """
    current_time = datetime.datetime.now()
    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 定义用于数据库状态存储的键
    active_air_pump_key = f"active_air_pump_{sn}"
    air_pump_start_time_key = f"air_pump_start_time_{sn}"
    
    # 检查气泵档位状态（停止、手动、自动）
    # 从设备数据结构中读取档位状态
    air_pump1_stop = new_data.get("air_pump1", {}).get("stop_status", 0) == 1
    air_pump2_stop = new_data.get("air_pump2", {}).get("stop_status", 0) == 1
    air_pump1_manual = new_data.get("air_pump1", {}).get("manual_status", 0) == 1
    air_pump2_manual = new_data.get("air_pump2", {}).get("manual_status", 0) == 1
    air_pump1_auto = new_data.get("air_pump1", {}).get("auto_status", 0) == 1
    air_pump2_auto = new_data.get("air_pump2", {}).get("auto_status", 0) == 1
    
    # 获取当前运行状态
    do23_status = new_data.get("DO23_status", 0)
    do24_status = new_data.get("DO24_status", 0)
    
    print(f"[{current_time_str}] [air_pump_logic] 档位状态检查:")
    print(f"  气泵1: 停止={air_pump1_stop}, 手动={air_pump1_manual}, 自动={air_pump1_auto}, 运行={do23_status}")
    print(f"  气泵2: 停止={air_pump2_stop}, 手动={air_pump2_manual}, 自动={air_pump2_auto}, 运行={do24_status}")
    
    # 优先处理停止和手动档位（这些不需要协调）
    handle_single_air_pump_control_non_auto(db, sn, "air_pump1", "DO23", 
                                           air_pump1_stop, air_pump1_manual, air_pump1_auto,
                                           do23_status, current_time_str, new_data)
                                           
    handle_single_air_pump_control_non_auto(db, sn, "air_pump2", "DO24",
                                           air_pump2_stop, air_pump2_manual, air_pump2_auto,
                                           do24_status, current_time_str, new_data)
    
    # 如果两个气泵都在自动模式，需要协调运行（互斥逻辑）
    if air_pump1_auto and air_pump2_auto and not (air_pump1_stop or air_pump2_stop or air_pump1_manual or air_pump2_manual):
        handle_coordinated_air_pump_auto_logic(db, sn, new_data, current_time_str)
    elif air_pump1_auto and not (air_pump1_stop or air_pump1_manual):
        # 只有气泵1在自动模式
        handle_single_air_pump_auto_logic(db, sn, "air_pump1", "DO23", do23_status, current_time_str, new_data)
    elif air_pump2_auto and not (air_pump2_stop or air_pump2_manual):
        # 只有气泵2在自动模式
        handle_single_air_pump_auto_logic(db, sn, "air_pump2", "DO24", do24_status, current_time_str, new_data)


def handle_single_air_pump_control_non_auto(db: Session, sn: str, pump_name: str, do_name: str,
                                           stop_active: bool, manual_active: bool, auto_active: bool,
                                           current_status: int, current_time_str: str, device_data: dict):
    """
    处理单个气泵的停止和手动档位控制逻辑（不包括自动档位）
    优先级：停止 > 手动
    """
    
    # 优先级1：停止档位（最高优先级）
    if stop_active:
        if current_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] {pump_name} 停止档位激活，强制停止")
            stop_air_pump(db, sn, pump_name, do_name, "停止档位激活")
        return
    
    # 优先级2：手动档位（中等优先级）
    elif manual_active:
        if current_status == 0:
            print(f"[{current_time_str}] [air_pump_logic] {pump_name} 手动档位激活，强制启动")
            start_air_pump(db, sn, pump_name, do_name, "手动档位激活")
        else:
            print(f"[{current_time_str}] [air_pump_logic] {pump_name} 手动档位激活，保持运行")
        return
    
    # 优先级3：无档位激活（最低优先级）
    elif not auto_active:
        if current_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] {pump_name} 无档位激活，停止运行")
            stop_air_pump(db, sn, pump_name, do_name, "无档位激活")


def handle_single_air_pump_auto_logic(db: Session, sn: str, pump_name: str, do_name: str,
                                     current_status: int, current_time_str: str, device_data: dict):
    """
    处理单个气泵的自动档位逻辑（仅在只有一个气泵处于自动模式时使用）
    """
    # 检查故障状态
    pump_faulty = is_pump_faulty(device_data, pump_name)
    
    if pump_faulty:
        if current_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] {pump_name} 自动档位检测到故障，停止运行")
            stop_air_pump(db, sn, pump_name, do_name, "自动档位故障停止")
    else:
        # 自动档位且无故障，应该运行
        if current_status == 0:
            print(f"[{current_time_str}] [air_pump_logic] {pump_name} 自动档位正常，启动运行")
            start_air_pump(db, sn, pump_name, do_name, "自动档位启动")
        else:
            print(f"[{current_time_str}] [air_pump_logic] {pump_name} 自动档位正常，保持运行")


def handle_coordinated_air_pump_auto_logic(db: Session, sn: str, new_data: dict, current_time_str: str):
    """
    处理两个气泵都在自动档位时的协调运行逻辑
    策略：
    1. 检测故障状态，故障的气泵停止运行
    2. 如果一个气泵故障，另一个气泵持续运行  
    3. 如果都正常，只运行一个气泵（优先气泵1）
    4. 如果当前运行的气泵关闭，另一个气泵自动接管
    """
    active_air_pump_key = f"active_air_pump_{sn}"
    air_pump_start_time_key = f"air_pump_start_time_{sn}"
    
    # 获取当前运行状态
    do23_status = new_data.get("DO23_status", 0)
    do24_status = new_data.get("DO24_status", 0)
    
    # 检查故障状态
    air_pump1_faulty = is_pump_faulty(new_data, "air_pump1")
    air_pump2_faulty = is_pump_faulty(new_data, "air_pump2")
    
    print(f"[{current_time_str}] [air_pump_logic] 协调运行检查: 气泵1故障={air_pump1_faulty}, 气泵2故障={air_pump2_faulty}")
    
    # 获取当前活跃的气泵
    current_active_pump = crud.get_kv(db, active_air_pump_key)
    
    # 情况1：两个气泵都故障
    if air_pump1_faulty and air_pump2_faulty:
        if do23_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] 两个气泵都故障，停止气泵1")
            stop_air_pump(db, sn, "air_pump1", "DO23", "两气泵都故障")
        if do24_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] 两个气泵都故障，停止气泵2")
            stop_air_pump(db, sn, "air_pump2", "DO24", "两气泵都故障")
        return
    
    # 情况2：只有气泵1故障，气泵2应该运行
    elif air_pump1_faulty and not air_pump2_faulty:
        if do23_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] 气泵1故障，停止运行")
            stop_air_pump(db, sn, "air_pump1", "DO23", "气泵1故障切换")
        if do24_status == 0:
            print(f"[{current_time_str}] [air_pump_logic] 气泵1故障，启动气泵2接管")
            start_air_pump(db, sn, "air_pump2", "DO24", "故障切换启动")
        else:
            print(f"[{current_time_str}] [air_pump_logic] 气泵2正常运行中（气泵1故障）")
        return
    
    # 情况3：只有气泵2故障，气泵1应该运行
    elif air_pump2_faulty and not air_pump1_faulty:
        if do24_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] 气泵2故障，停止运行")
            stop_air_pump(db, sn, "air_pump2", "DO24", "气泵2故障切换")
        if do23_status == 0:
            print(f"[{current_time_str}] [air_pump_logic] 气泵2故障，启动气泵1接管")
            start_air_pump(db, sn, "air_pump1", "DO23", "故障切换启动")
        else:
            print(f"[{current_time_str}] [air_pump_logic] 气泵1正常运行中（气泵2故障）")
        return
    
    # 情况4：两个气泵都正常，实现互斥运行逻辑
    else:
        # 检查是否有双泵运行的异常情况
        if do23_status == 1 and do24_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] 检测到双气泵运行异常，执行互斥修复")
            
            # 根据数据库中的活跃气泵来决定保留哪个
            if current_active_pump == "air_pump1":
                print(f"[{current_time_str}] [air_pump_logic] 保留气泵1，停止气泵2")
                stop_air_pump(db, sn, "air_pump2", "DO24", "双泵运行修复")
            elif current_active_pump == "air_pump2":
                print(f"[{current_time_str}] [air_pump_logic] 保留气泵2，停止气泵1")
                stop_air_pump(db, sn, "air_pump1", "DO23", "双泵运行修复")
            else:
                # 默认保留气泵1，停止气泵2
                print(f"[{current_time_str}] [air_pump_logic] 数据库状态不明，默认保留气泵1，停止气泵2")
                stop_air_pump(db, sn, "air_pump2", "DO24", "双泵运行修复默认")
                crud.set_kv(db, active_air_pump_key, "air_pump1")
                crud.set_kv(db, air_pump_start_time_key, datetime.datetime.now().isoformat())
        
        # 确保至少有一个气泵在运行（"一个关闭时另一个运行"的逻辑）
        elif do23_status == 0 and do24_status == 0:
            # 两个都没运行，启动优先气泵（根据活跃状态或默认气泵1）
            preferred_pump = current_active_pump or "air_pump1"
            if preferred_pump == "air_pump1":
                print(f"[{current_time_str}] [air_pump_logic] 无气泵运行，启动气泵1")
                start_air_pump(db, sn, "air_pump1", "DO23", "自动启动首选")
            else:
                print(f"[{current_time_str}] [air_pump_logic] 无气泵运行，启动气泵2")
                start_air_pump(db, sn, "air_pump2", "DO24", "自动启动首选")
                
        # 正常情况：只有一个气泵在运行
        elif do23_status == 1 and do24_status == 0:
            print(f"[{current_time_str}] [air_pump_logic] 气泵1正常运行，气泵2停止")
            # 确保数据库状态正确
            if current_active_pump != "air_pump1":
                crud.set_kv(db, active_air_pump_key, "air_pump1")
                if not crud.get_kv(db, air_pump_start_time_key):
                    crud.set_kv(db, air_pump_start_time_key, datetime.datetime.now().isoformat())
                    
        elif do23_status == 0 and do24_status == 1:
            print(f"[{current_time_str}] [air_pump_logic] 气泵2正常运行，气泵1停止")
            # 确保数据库状态正确
            if current_active_pump != "air_pump2":
                crud.set_kv(db, active_air_pump_key, "air_pump2")
                if not crud.get_kv(db, air_pump_start_time_key):
                    crud.set_kv(db, air_pump_start_time_key, datetime.datetime.now().isoformat())


def stop_air_pump(db: Session, sn: str, pump_name: str, do_name: str, reason: str):
    """
    停止指定的气泵
    """
    current_time = datetime.datetime.now()
    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 记录操作日志
    pump_log = crud.create_operation_log(
        db=db,
        operation_type="air_pump_control",
        operation_details=f"{reason}，停止 {pump_name} ({do_name})",
        device_sn=sn,
        additional_data={
            "pump_name": pump_name,
            "do_name": do_name,
            "trigger": reason
        }
    )
    
    # 停止气泵
    wait_for_device_ready(sn)
    command = create_do_command(do_name=do_name, value=0)
    success, _ = send_command_to_device(sn=sn, command=command)
    
    if success:
        # 清理状态（如果停止的是当前活跃的气泵）
        active_air_pump = crud.get_kv(db, f"active_air_pump_{sn}")
        if active_air_pump == pump_name:
            crud.set_kv(db, f"active_air_pump_{sn}", "")
            crud.set_kv(db, f"air_pump_start_time_{sn}", "")
        
        crud.update_operation_log_status(db, pump_log.id, "success")
        print(f"[{current_time_str}] [air_pump_logic] {pump_name}停止成功")
    else:
        crud.update_operation_log_status(db, pump_log.id, "failed", "设备命令发送失败")
        print(f"[{current_time_str}] [air_pump_logic] {pump_name}停止失败")


def start_air_pump(db: Session, sn: str, pump_name: str, do_name: str, reason: str):
    """
    启动指定的气泵
    """
    current_time = datetime.datetime.now()
    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 记录操作日志
    pump_log = crud.create_operation_log(
        db=db,
        operation_type="air_pump_control",
        operation_details=f"{reason}，启动 {pump_name} ({do_name})",
        device_sn=sn,
        additional_data={
            "pump_name": pump_name,
            "do_name": do_name,
            "trigger": reason
        }
    )
    
    # 启动气泵
    wait_for_device_ready(sn)
    command = create_do_command(do_name=do_name, value=1)
    success, _ = send_command_to_device(sn=sn, command=command)
    
    if success:
        # 更新状态
        crud.set_kv(db, f"active_air_pump_{sn}", pump_name)
        crud.set_kv(db, f"air_pump_start_time_{sn}", current_time.isoformat())
        crud.update_operation_log_status(db, pump_log.id, "success")
        print(f"[{current_time_str}] [air_pump_logic] {pump_name}启动成功")
    else:
        crud.update_operation_log_status(db, pump_log.id, "failed", "设备命令发送失败")
        print(f"[{current_time_str}] [air_pump_logic] {pump_name}启动失败")


def is_pump_faulty(device_data: dict, pump_name: str) -> bool:
    """
    检测水泵或气泵是否有故障
    故障条件：
    1. DO输出但设备实际没运行（硬件故障）
    2. 电流异常（过低表示负载异常，过高表示过载）
    
    注意：只有在有有效电流数据时才进行电流检查
    """
    
    # 检查DO状态与实际运行状态是否匹配（如果有running_status字段）
    if pump_name == "water_pump1":
        do_status = device_data.get("DO21_status", 0)
        # 注意：当前设备数据中没有running_status字段，先跳过这个检查
        # actual_status = device_data.get("water_pump1", {}).get("running_status", 0)
        # if do_status == 1 and actual_status == 0:
        #     return True
    elif pump_name == "water_pump2":
        do_status = device_data.get("DO22_status", 0)
        # actual_status = device_data.get("water_pump2", {}).get("running_status", 0)
        # if do_status == 1 and actual_status == 0:
        #     return True
    elif pump_name == "air_pump1":
        do_status = device_data.get("DO23_status", 0)
        # actual_status = device_data.get("air_pump1", {}).get("running_status", 0)
        # if do_status == 1 and actual_status == 0:
        #     return True
    elif pump_name == "air_pump2":
        do_status = device_data.get("DO24_status", 0)
        # actual_status = device_data.get("air_pump2", {}).get("running_status", 0)
        # if do_status == 1 and actual_status == 0:
        #     return True
    
    # 检查电流异常（只有在有有效数值电流数据时才检查）
    current_data = device_data.get("dianliucaiji2", {})
    if pump_name == "water_pump1":
        # 水泵1电流检查 - 使用ch1
        current_value = current_data.get("curr2_ch1", 0)
        # 只有在有有效数值数据且不为空字符串时才进行故障检查
        if isinstance(current_value, (int, float)) and current_value != 0:
            if current_value < 0.5 or current_value > 20:  # 调整阈值，避免正常低电流被误判
                return True
    elif pump_name == "water_pump2":
        # 水泵2电流检查 - 使用ch2
        current_value = current_data.get("curr2_ch2", 0)
        if isinstance(current_value, (int, float)) and current_value != 0:
            if current_value < 0.5 or current_value > 20:
                return True
    elif pump_name == "air_pump1":
        # 气泵1电流检查 - 使用ch3
        current_value = current_data.get("curr2_ch3", 0)
        if isinstance(current_value, (int, float)) and current_value != 0:
            if current_value < 0.3 or current_value > 10:  # 气泵电流阈值相对较低
                return True
    elif pump_name == "air_pump2":
        # 气泵2电流检查 - 使用ch4
        current_value = current_data.get("curr2_ch4", 0)
        if isinstance(current_value, (int, float)) and current_value != 0:
            if current_value < 0.3 or current_value > 10:
                return True
    
    # 默认无故障
    return False
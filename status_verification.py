# /f:/水利站/backend/status_verification.py
import datetime
import threading
from database import SessionLocal
import crud
from data_store import DEVICE_DATA, DATA_LOCK

# 状态验证相关的映射
PUMP_TO_DO_STATUS_MAPPING = {
    "water_pump1": "DO21_status",
    "water_pump2": "DO22_status",
    "air_pump1": "DO23_status", 
    "air_pump2": "DO24_status",
}


def verify_device_status(sn: str, expected_pump: str, expected_status: int, timeout_seconds: int = 10) -> bool:
    """
    验证设备的实际DO状态是否与期望一致。
    
    :param sn: 设备序列号
    :param expected_pump: 期望控制的水泵名称
    :param expected_status: 期望的状态 (0或1)
    :param timeout_seconds: 验证超时时间（秒）
    :return: True表示状态一致，False表示不一致或超时
    """
    do_status_key = PUMP_TO_DO_STATUS_MAPPING.get(expected_pump)
    if not do_status_key:
        print(f"[status_verification] 未知的水泵名称: {expected_pump}")
        return False
    
    start_time = datetime.datetime.now()
    current_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    
    print(f"[{current_time_str}] [status_verification] 开始验证设备 {sn} 的 {expected_pump} 状态")
    
    while True:
        # 检查超时
        elapsed = (datetime.datetime.now() - start_time).total_seconds()
        if elapsed >= timeout_seconds:
            current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{current_time_str}] [status_verification] 验证超时 ({timeout_seconds}秒): 设备 {sn} 的 {expected_pump}")
            return False
        
        # 获取当前设备数据
        with DATA_LOCK:
            device_data = DEVICE_DATA.get(sn, {})
            actual_status = device_data.get(do_status_key)
        
        if actual_status is not None:
            if actual_status == expected_status:
                current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{current_time_str}] [status_verification] 验证成功: 设备 {sn} 的 {expected_pump} 状态为 {actual_status}")
                return True
            else:
                # 状态不匹配，继续等待
                pass
        
        # 短暂等待后重试
        threading.Event().wait(0.5)


def log_status_verification_result(
    sn: str, 
    pump_name: str, 
    expected_status: int,
    actual_status: int | None,
    verification_success: bool,
    related_log_id: int
):
    """
    记录状态验证结果到操作日志。
    
    :param sn: 设备序列号
    :param pump_name: 水泵名称
    :param expected_status: 期望状态
    :param actual_status: 实际状态
    :param verification_success: 验证是否成功
    :param related_log_id: 关联的主操作日志ID
    """
    db = SessionLocal()
    try:
        status_details = (
            f"状态验证 - 水泵: {pump_name}, 期望: {expected_status}, "
            f"实际: {actual_status}, 结果: {'成功' if verification_success else '失败'}"
        )
        
        crud.create_operation_log(
            db=db,
            operation_type="status_verification",
            operation_details=status_details,
            device_sn=sn,
            execution_status="success" if verification_success else "failed",
            additional_data={
                "pump_name": pump_name,
                "expected_status": expected_status,
                "actual_status": actual_status,
                "verification_success": verification_success,
                "related_log_id": related_log_id
            }
        )
    except Exception as e:
        print(f"记录状态验证日志失败: {e}")
    finally:
        db.close()


def verify_and_log_pump_status(
    sn: str, 
    pump_name: str, 
    expected_status: int, 
    related_log_id: int,
    timeout_seconds: int = 10
):
    """
    验证水泵状态并记录验证结果。
    这个函数应该在发送命令后异步调用。
    
    :param sn: 设备序列号
    :param pump_name: 水泵名称
    :param expected_status: 期望状态
    :param related_log_id: 关联的主操作日志ID
    :param timeout_seconds: 验证超时时间
    """
    def verification_task():
        verification_success = verify_device_status(sn, pump_name, expected_status, timeout_seconds)
        
        # 获取实际状态用于日志记录
        with DATA_LOCK:
            do_status_key = PUMP_TO_DO_STATUS_MAPPING.get(pump_name)
            actual_status = DEVICE_DATA.get(sn, {}).get(do_status_key) if do_status_key else None
        
        log_status_verification_result(
            sn, pump_name, expected_status, actual_status, 
            verification_success, related_log_id
        )
        
        return verification_success
    
    # 在后台线程中执行验证
    verification_thread = threading.Thread(target=verification_task, daemon=True)
    verification_thread.start()
[{"id": "config_update_tab", "type": "tab", "label": "配置文件更新", "disabled": false, "info": "从服务器文件读取配置并更新Node-RED流程"}, {"id": "manual_trigger", "type": "inject", "z": "config_update_tab", "name": "手动触发更新", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 150, "y": 100, "wires": [["read_config_file"]]}, {"id": "auto_trigger", "type": "inject", "z": "config_update_tab", "name": "自动定时更新(30分钟)", "props": [{"p": "payload"}], "repeat": "1800", "crontab": "", "once": true, "onceDelay": 10, "topic": "", "payload": "", "payloadType": "date", "x": 180, "y": 160, "wires": [["read_config_file"]]}, {"id": "read_config_file", "type": "file in", "z": "config_update_tab", "name": "读取配置文件", "filename": "/home/<USER>/tcp-server/config/flow-config.json", "format": "utf8", "chunk": false, "sendError": false, "encoding": "none", "allProps": false, "x": 420, "y": 130, "wires": [["parse_config"]]}, {"id": "parse_config", "type": "function", "z": "config_update_tab", "name": "解析配置文件", "func": "try {\n    // 解析JSON配置文件\n    var config = JSON.parse(msg.payload);\n    \n    node.log('=== 配置文件解析成功 ===');\n    node.log('配置版本: ' + (config.version || '未知'));\n    node.log('更新时间: ' + (config.updated || '未知'));\n    \n    // 验证配置文件结构\n    if (!config.timers || !config.devices || !config.priorities) {\n        throw new Error('配置文件格式不正确，缺少必要字段');\n    }\n    \n    msg.config = config;\n    msg.updateType = 'config_parsed';\n    \n    node.log('配置文件验证通过，准备更新流程');\n    return msg;\n    \n} catch (error) {\n    node.error('配置文件解析失败: ' + error.message);\n    msg.error = error.message;\n    msg.updateType = 'parse_error';\n    return msg;\n}", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 620, "y": 130, "wires": [["config_switch"]]}, {"id": "config_switch", "type": "switch", "z": "config_update_tab", "name": "配置处理分发", "property": "updateType", "propertyType": "msg", "rules": [{"t": "eq", "v": "config_parsed", "vt": "str"}, {"t": "eq", "v": "parse_error", "vt": "str"}], "checkall": "false", "repair": false, "outputs": 2, "x": 820, "y": 130, "wires": [["backup_current_flow"], ["error_handler"]]}, {"id": "backup_current_flow", "type": "function", "z": "config_update_tab", "name": "备份当前流程", "func": "// 获取当前时间戳\nvar timestamp = new Date().toISOString().replace(/[:.]/g, '-');\nvar backupPath = '/home/<USER>/tcp-server/backup/flows-backup-' + timestamp + '.json';\n\n// 设置备份文件路径\nmsg.backupPath = backupPath;\nmsg.filename = '/home/<USER>/tcp-server/flows.json';\n\nnode.log('准备备份当前流程到: ' + backupPath);\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1040, "y": 100, "wires": [["read_current_flow"]]}, {"id": "read_current_flow", "type": "file in", "z": "config_update_tab", "name": "读取当前flows.json", "filename": "", "format": "utf8", "chunk": false, "sendError": false, "encoding": "none", "allProps": false, "x": 1260, "y": 100, "wires": [["save_backup"]]}, {"id": "save_backup", "type": "file", "z": "config_update_tab", "name": "保存备份文件", "filename": "", "filenameType": "msg", "appendNewline": false, "createDir": true, "overwriteFile": "true", "encoding": "none", "x": 1460, "y": 100, "wires": [["update_flow_config"]]}, {"id": "update_flow_config", "type": "function", "z": "config_update_tab", "name": "更新流程配置", "func": "try {\n    // 解析当前flows.json\n    var currentFlows = JSON.parse(msg.payload);\n    var config = msg.config;\n    \n    node.log('=== 开始更新流程配置 ===');\n    \n    // 更新定时器配置\n    if (config.timers) {\n        updateTimerIntervals(currentFlows, config.timers);\n        node.log('定时器配置已更新');\n    }\n    \n    // 更新设备映射配置\n    if (config.devices) {\n        updateDeviceMappings(currentFlows, config.devices);\n        node.log('设备映射配置已更新');\n    }\n    \n    // 更新控制优先级配置\n    if (config.priorities) {\n        updateControlPriorities(currentFlows, config.priorities);\n        node.log('控制优先级配置已更新');\n    }\n    \n    // 更新调试配置\n    if (config.debug) {\n        updateDebugSettings(currentFlows, config.debug);\n        node.log('调试配置已更新');\n    }\n    \n    msg.updatedFlows = JSON.stringify(currentFlows, null, 2);\n    msg.updateResult = 'success';\n    \n    node.log('=== 流程配置更新完成 ===');\n    return msg;\n    \n} catch (error) {\n    node.error('更新流程配置失败: ' + error.message);\n    msg.error = error.message;\n    msg.updateResult = 'error';\n    return msg;\n}\n\n// 更新定时器间隔\nfunction updateTimerIntervals(flows, timerConfig) {\n    flows.forEach(function(node) {\n        if (node.type === 'inject') {\n            switch(node.name) {\n                case '浮球状态检查':\n                    if (timerConfig.float_check_seconds) {\n                        node.repeat = timerConfig.float_check_seconds.toString();\n                    }\n                    break;\n                case '档位状态读取':\n                    if (timerConfig.position_read_seconds) {\n                        node.repeat = timerConfig.position_read_seconds.toString();\n                    }\n                    break;\n                case '手动档位控制检查':\n                    if (timerConfig.manual_control_seconds) {\n                        node.repeat = timerConfig.manual_control_seconds.toString();\n                    }\n                    break;\n                case '故障灯检查':\n                    if (timerConfig.fault_light_seconds) {\n                        node.repeat = timerConfig.fault_light_seconds.toString();\n                    }\n                    break;\n            }\n        }\n    });\n}\n\n// 更新设备映射\nfunction updateDeviceMappings(flows, deviceConfig) {\n    flows.forEach(function(node) {\n        if (node.type === 'function' && node.name === '三档位控制逻辑') {\n            // 更新设备映射数组\n            var funcCode = node.func;\n            if (deviceConfig.mappings) {\n                var newMappings = JSON.stringify(deviceConfig.mappings, null, 4);\n                funcCode = funcCode.replace(\n                    /var devices = \\[[\\s\\S]*?\\];/,\n                    'var devices = ' + newMappings + ';'\n                );\n                node.func = funcCode;\n            }\n        }\n    });\n}\n\n// 更新控制优先级\nfunction updateControlPriorities(flows, priorityConfig) {\n    flows.forEach(function(node) {\n        if (node.type === 'function' && node.name === '三档位控制逻辑') {\n            var funcCode = node.func;\n            \n            // 更新优先级注释\n            if (priorityConfig.order) {\n                var priorityComment = '// 三档位控制逻辑：' + priorityConfig.order.join(' > ');\n                funcCode = funcCode.replace(\n                    /\\/\\/ 三档位控制逻辑：[^\\n]*/,\n                    priorityComment\n                );\n            }\n            \n            // 更新气泵自动模式行为\n            if (priorityConfig.air_pump_auto_behavior !== undefined) {\n                var behavior = priorityConfig.air_pump_auto_behavior ? 'float_status' : '0';\n                funcCode = funcCode.replace(\n                    /control_value = 0;[\\s]*reason = [^;]*自动档位激活（气泵不受浮球控制）/,\n                    'control_value = ' + behavior + ';\\n            reason = device.auto_di + \"自动档位激活（气泵控制模式）\"'\n                );\n            }\n            \n            node.func = funcCode;\n        }\n    });\n}\n\n// 更新调试设置\nfunction updateDebugSettings(flows, debugConfig) {\n    flows.forEach(function(node) {\n        if (node.type === 'debug') {\n            if (debugConfig.console_output !== undefined) {\n                node.console = debugConfig.console_output;\n            }\n            if (debugConfig.active !== undefined) {\n                node.active = debugConfig.active;\n            }\n        }\n    });\n}", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1660, "y": 100, "wires": [["update_result_switch"]]}, {"id": "update_result_switch", "type": "switch", "z": "config_update_tab", "name": "更新结果分发", "property": "updateResult", "propertyType": "msg", "rules": [{"t": "eq", "v": "success", "vt": "str"}, {"t": "eq", "v": "error", "vt": "str"}], "checkall": "false", "repair": false, "outputs": 2, "x": 1880, "y": 100, "wires": [["save_updated_flow"], ["error_handler"]]}, {"id": "save_updated_flow", "type": "file", "z": "config_update_tab", "name": "保存更新后的flows.json", "filename": "/home/<USER>/tcp-server/flows.json", "appendNewline": false, "createDir": false, "overwriteFile": "true", "encoding": "none", "x": 2130, "y": 80, "wires": [["reload_flows"]]}, {"id": "reload_flows", "type": "function", "z": "config_update_tab", "name": "重新加载流程", "func": "// 发送重新加载命令到Node-RED API\nmsg.url = 'http://localhost:1880/flows';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json',\n    'Node-RED-Deployment-Type': 'reload'\n};\nmsg.payload = msg.updatedFlows;\n\nnode.log('准备重新加载Node-RED流程');\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2380, "y": 80, "wires": [["http_request"]]}, {"id": "http_request", "type": "http request", "z": "config_update_tab", "name": "重新部署流程", "method": "use", "ret": "txt", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "authType": "", "senderr": false, "headers": [], "x": 2580, "y": 80, "wires": [["success_handler"]]}, {"id": "success_handler", "type": "function", "z": "config_update_tab", "name": "成功处理", "func": "var timestamp = new Date().toLocaleString('zh-CN');\n\nnode.log('=== 流程更新成功完成 ===');\nnode.log('更新时间: ' + timestamp);\nnode.log('备份文件: ' + msg.backupPath);\nnode.log('配置版本: ' + (msg.config.version || '未知'));\n\n// 发送成功通知\nmsg.notification = {\n    type: 'success',\n    title: '流程更新成功',\n    message: '配置文件已成功应用，流程已重新部署',\n    timestamp: timestamp,\n    backup: msg.backupPath\n};\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 2780, "y": 80, "wires": [["notification_debug"]]}, {"id": "error_handler", "type": "function", "z": "config_update_tab", "name": "错误处理", "func": "var timestamp = new Date().toLocaleString('zh-CN');\n\nnode.error('=== 流程更新失败 ===');\nnode.error('错误时间: ' + timestamp);\nnode.error('错误信息: ' + (msg.error || '未知错误'));\n\n// 发送错误通知\nmsg.notification = {\n    type: 'error',\n    title: '流程更新失败',\n    message: msg.error || '未知错误',\n    timestamp: timestamp\n};\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1060, "y": 180, "wires": [["notification_debug"]]}, {"id": "notification_debug", "type": "debug", "z": "config_update_tab", "name": "通知输出", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "notification", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 2980, "y": 130, "wires": []}, {"id": "file_watcher", "type": "watch", "z": "config_update_tab", "name": "监控配置文件变化", "files": "/home/<USER>/tcp-server/config/flow-config.json", "recursive": "", "x": 180, "y": 240, "wires": [["file_change_filter"]]}, {"id": "file_change_filter", "type": "function", "z": "config_update_tab", "name": "文件变化过滤", "func": "// 只处理文件修改事件，忽略临时文件\nif (msg.type === 'change' && !msg.file.includes('~') && !msg.file.includes('.tmp')) {\n    node.log('检测到配置文件变化: ' + msg.file);\n    \n    // 延迟2秒后触发更新，避免文件写入未完成\n    setTimeout(function() {\n        node.send({payload: new Date()});\n    }, 2000);\n    \n    return null;\n}\n\nreturn null;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 240, "wires": [["read_config_file"]]}]
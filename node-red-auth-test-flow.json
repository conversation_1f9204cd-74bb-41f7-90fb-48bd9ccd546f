[{"id": "auth_test_tab", "type": "tab", "label": "Node-RED认证测试", "disabled": false, "info": "测试Node-RED自动化登录功能，使用admin/admin账号密码"}, {"id": "manual_auth_test", "type": "inject", "z": "auth_test_tab", "name": "手动测试登录", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 140, "y": 100, "wires": [["prepare_auth_request"]]}, {"id": "auto_auth_test", "type": "inject", "z": "auth_test_tab", "name": "自动测试登录(10秒)", "props": [{"p": "payload"}], "repeat": "10", "crontab": "", "once": true, "onceDelay": 2, "topic": "", "payload": "", "payloadType": "date", "x": 160, "y": 160, "wires": [["prepare_auth_request"]]}, {"id": "prepare_auth_request", "type": "function", "z": "auth_test_tab", "name": "准备认证请求", "func": "// 设置认证API请求参数\nmsg.url = 'http://localhost:1880/auth/token';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n};\n\n// 设置认证凭据\nmsg.payload = {\n    username: 'admin',\n    password: 'admin',\n    client_id: 'node-red-admin',\n    grant_type: 'password',\n    scope: '*'\n};\n\nnode.log('=== 准备Node-RED认证请求 ===');\nnode.log('目标URL: ' + msg.url);\nnode.log('用户名: admin');\nnode.log('请求时间: ' + new Date().toLocaleString('zh-CN'));\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 400, "y": 130, "wires": [["auth_request"]]}, {"id": "auth_request", "type": "http request", "z": "auth_test_tab", "name": "认证请求", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "authType": "", "senderr": false, "headers": [], "x": 620, "y": 130, "wires": [["auth_response_handler"]]}, {"id": "auth_response_handler", "type": "function", "z": "auth_test_tab", "name": "认证响应处理", "func": "var timestamp = new Date().toLocaleString('zh-CN');\n\nnode.log('=== Node-RED认证响应处理 ===');\nnode.log('响应时间: ' + timestamp);\nnode.log('状态码: ' + (msg.statusCode || '未知'));\n\ntry {\n    if (msg.statusCode === 200 && msg.payload) {\n        // 认证成功\n        var tokenData = msg.payload;\n        \n        if (tokenData.access_token) {\n            node.log('✅ 认证成功！');\n            node.log('访问令牌: ' + tokenData.access_token.substring(0, 20) + '...');\n            node.log('令牌类型: ' + (tokenData.token_type || 'Bearer'));\n            node.log('过期时间: ' + (tokenData.expires_in || '未知') + '秒');\n            \n            // 存储token到全局变量\n            global.set('nodeRedAuthToken', tokenData.access_token);\n            global.set('nodeRedTokenExpiry', Date.now() + (tokenData.expires_in * 1000));\n            \n            msg.authResult = {\n                success: true,\n                token: tokenData.access_token,\n                tokenType: tokenData.token_type || 'Bearer',\n                expiresIn: tokenData.expires_in,\n                timestamp: timestamp\n            };\n            \n            node.warn('🎉 Node-RED自动登录成功！');\n            \n        } else {\n            node.log('❌ 认证响应格式错误，缺少access_token');\n            msg.authResult = {\n                success: false,\n                error: '响应格式错误',\n                response: tokenData,\n                timestamp: timestamp\n            };\n        }\n        \n    } else {\n        // 认证失败\n        node.log('❌ 认证失败');\n        node.log('错误信息: ' + JSON.stringify(msg.payload || msg.error || '未知错误'));\n        \n        msg.authResult = {\n            success: false,\n            statusCode: msg.statusCode,\n            error: msg.payload || msg.error || '认证请求失败',\n            timestamp: timestamp\n        };\n    }\n    \n} catch (error) {\n    node.error('认证响应处理异常: ' + error.message);\n    msg.authResult = {\n        success: false,\n        error: '响应处理异常: ' + error.message,\n        timestamp: timestamp\n    };\n}\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 840, "y": 130, "wires": [["auth_result_switch"]]}, {"id": "auth_result_switch", "type": "switch", "z": "auth_test_tab", "name": "认证结果分发", "property": "authResult.success", "propertyType": "msg", "rules": [{"t": "true"}, {"t": "false"}], "checkall": "false", "repair": false, "outputs": 2, "x": 1080, "y": 130, "wires": [["success_handler"], ["error_handler"]]}, {"id": "success_handler", "type": "function", "z": "auth_test_tab", "name": "成功处理", "func": "// 测试使用token访问Node-RED API\nvar token = msg.authResult.token;\n\nnode.log('=== 测试API访问 ===');\nnode.log('使用token测试API访问');\n\n// 准备测试API请求\nmsg.url = 'http://localhost:1880/flows';\nmsg.method = 'GET';\nmsg.headers = {\n    'Authorization': 'Bearer ' + token,\n    'Accept': 'application/json'\n};\nmsg.payload = '';\n\nnode.log('测试API端点: ' + msg.url);\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1280, "y": 100, "wires": [["api_test_request"]]}, {"id": "api_test_request", "type": "http request", "z": "auth_test_tab", "name": "API测试请求", "method": "use", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "authType": "", "senderr": false, "headers": [], "x": 1480, "y": 100, "wires": [["api_test_result"]]}, {"id": "api_test_result", "type": "function", "z": "auth_test_tab", "name": "API测试结果", "func": "node.log('=== API测试结果 ===');\nnode.log('状态码: ' + (msg.statusCode || '未知'));\n\nif (msg.statusCode === 200) {\n    var flowsCount = Array.isArray(msg.payload) ? msg.payload.length : 0;\n    node.log('✅ API访问成功！');\n    node.log('获取到 ' + flowsCount + ' 个流程节点');\n    \n    msg.testResult = {\n        auth_success: true,\n        api_success: true,\n        flows_count: flowsCount,\n        message: 'Node-RED自动化登录和API访问完全成功！'\n    };\n    \n    node.warn('🎯 完整测试成功：认证 + API访问');\n    \n} else {\n    node.log('❌ API访问失败');\n    node.log('错误: ' + JSON.stringify(msg.payload || '未知'));\n    \n    msg.testResult = {\n        auth_success: true,\n        api_success: false,\n        error: msg.payload || '未知API错误',\n        message: '认证成功但API访问失败'\n    };\n}\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1700, "y": 100, "wires": [["final_result_debug"]]}, {"id": "error_handler", "type": "function", "z": "auth_test_tab", "name": "错误处理", "func": "node.log('=== 认证失败处理 ===');\nnode.log('错误详情: ' + JSON.stringify(msg.authResult));\n\n// 常见错误诊断\nvar diagnosis = [];\n\nif (msg.authResult.statusCode === 404) {\n    diagnosis.push('认证端点不存在，请检查Node-RED是否启用了认证');\n} else if (msg.authResult.statusCode === 401) {\n    diagnosis.push('用户名或密码错误');\n} else if (msg.authResult.statusCode === 403) {\n    diagnosis.push('账号被禁用或权限不足');\n} else if (!msg.authResult.statusCode) {\n    diagnosis.push('网络连接问题，请检查Node-RED是否运行在1880端口');\n}\n\nif (diagnosis.length > 0) {\n    node.log('💡 可能原因: ' + diagnosis.join('; '));\n}\n\nmsg.testResult = {\n    auth_success: false,\n    api_success: false,\n    error: msg.authResult.error,\n    diagnosis: diagnosis,\n    message: 'Node-RED自动化登录失败'\n};\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1280, "y": 160, "wires": [["final_result_debug"]]}, {"id": "final_result_debug", "type": "debug", "z": "auth_test_tab", "name": "最终测试结果", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "testResult", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1920, "y": 130, "wires": []}, {"id": "token_info_display", "type": "inject", "z": "auth_test_tab", "name": "查看已保存的Token", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 160, "y": 240, "wires": [["display_token_info"]]}, {"id": "display_token_info", "type": "function", "z": "auth_test_tab", "name": "显示Token信息", "func": "var token = global.get('nodeRedAuthToken');\nvar expiry = global.get('nodeRedTokenExpiry');\n\nnode.log('=== 当前保存的Token信息 ===');\n\nif (token) {\n    var isExpired = expiry && Date.now() > expiry;\n    var remainingTime = expiry ? Math.max(0, expiry - Date.now()) : 0;\n    \n    node.log('Token: ' + token.substring(0, 20) + '...');\n    node.log('过期状态: ' + (isExpired ? '已过期' : '有效'));\n    \n    if (!isExpired && remainingTime > 0) {\n        var remainingMinutes = Math.floor(remainingTime / 60000);\n        node.log('剩余时间: ' + remainingMinutes + '分钟');\n    }\n    \n    msg.tokenInfo = {\n        hasToken: true,\n        tokenPreview: token.substring(0, 20) + '...',\n        isExpired: isExpired,\n        remainingMinutes: Math.floor(remainingTime / 60000),\n        timestamp: new Date().toLocaleString('zh-CN')\n    };\n    \n} else {\n    node.log('❌ 没有保存的Token');\n    msg.tokenInfo = {\n        hasToken: false,\n        message: '没有找到保存的认证Token',\n        timestamp: new Date().toLocaleString('zh-CN')\n    };\n}\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 400, "y": 240, "wires": [["token_info_debug"]]}, {"id": "token_info_debug", "type": "debug", "z": "auth_test_tab", "name": "Token信息", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "tokenInfo", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 620, "y": 240, "wires": []}, {"id": "clear_token", "type": "inject", "z": "auth_test_tab", "name": "清除保存的Token", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 150, "y": 300, "wires": [["clear_token_function"]]}, {"id": "clear_token_function", "type": "function", "z": "auth_test_tab", "name": "清除Token", "func": "// 清除全局变量中的token\nglobal.set('nodeRedAuthToken', null);\nglobal.set('nodeRedTokenExpiry', null);\n\nnode.log('=== Token已清除 ===');\nnode.warn('已清除保存的认证Token');\n\nmsg.payload = {\n    action: 'token_cleared',\n    timestamp: new Date().toLocaleString('zh-CN'),\n    message: '认证Token已从全局变量中清除'\n};\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 370, "y": 300, "wires": [["clear_result_debug"]]}, {"id": "clear_result_debug", "type": "debug", "z": "auth_test_tab", "name": "清除结果", "active": true, "tosidebar": true, "console": true, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 580, "y": 300, "wires": []}]
# /f:/水利站/backend/scheduler_service.py
import datetime
import uuid
import threading
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.jobstores.base import JobLookupError
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from sqlalchemy.orm import Session

from device_control import create_do_command, send_command_to_device
from database import SQLALCHEMY_DATABASE_URL, SessionLocal
import crud

# -- 修改开始: 配置持久化作业存储 --
jobstores = {"default": SQLAlchemyJobStore(url=SQLALCHEMY_DATABASE_URL)}
scheduler = BackgroundScheduler(jobstores=jobstores, timezone="Asia/Shanghai")
# -- 修改结束 --

# 线程安全的字典，用于存储和管理定时任务
# 结构: { "job_id": {"sn": "...", "do_name": "...", ...} }
SCHEDULED_TASKS = {}
TASKS_LOCK = threading.Lock()

# -- 新增循环任务管理 --
CYCLIC_TASKS = {}
CYCLIC_TASKS_LOCK = threading.Lock()

# -- 新增顺序循环任务管理 --
SEQUENCE_TASKS = {}
SEQUENCE_TASKS_LOCK = threading.Lock()
# -- 循环任务管理结束 --


def _execute_task(sn: str, do_name: str, value: int, job_id: str):
    """
    调度器实际执行的函数。
    """
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(
        f"[{current_time}] [scheduler] 执行任务 {job_id}: 控制设备 {sn} 的 {do_name} -> {'闭合' if value==1 else '断开'}"
    )
    command = create_do_command(do_name=do_name, value=value)
    send_command_to_device(sn=sn, command=command)

    # 任务执行完毕后，从列表中移除
    with TASKS_LOCK:
        SCHEDULED_TASKS.pop(job_id, None)
    print(f"[{current_time}] [scheduler] 任务 {job_id} 执行完毕并已移除。")


# -- 新增循环任务执行函数 --
def _execute_cyclic_task(sn: str, do_name: str, value: int):
    """
    调度器为循环任务实际执行的函数
    """
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(
        f"[{current_time}] [scheduler] 执行循环任务: 控制设备 {sn} 的 {do_name} -> {'闭合' if value==1 else '断开'}"
    )
    try:
        command = create_do_command(do_name=do_name, value=value)
        send_command_to_device(sn=sn, command=command)
        print(f"[{current_time}] [scheduler] 循环任务执行成功: {sn}.{do_name}={value}")
    except Exception as e:
        print(f"[{current_time}] [scheduler] 循环任务执行失败: {sn}.{do_name}={value}, 错误: {e}")
        # 记录到操作日志
        db = SessionLocal()
        try:
            import crud
            crud.create_operation_log(
                db,
                operation_type="scheduler_cyclic_task",
                operation_details=f"循环任务执行失败: {sn}.{do_name}={value}",
                execution_status="failed",
                error_message=str(e),
                device_sn=sn
            )
        except Exception as log_error:
            print(f"[{current_time}] [scheduler] 记录操作日志失败: {log_error}")
        finally:
            db.close()


# -- 新增结束 --


def _restore_state_from_db():
    """
    从数据库恢复任务元数据到内存。
    在服务启动时调用。
    """
    print("[scheduler] 正在从数据库恢复任务状态...")
    db = SessionLocal()
    try:
        all_metadata = crud.get_all_task_metadata(db)
        for task in all_metadata:
            task_id = str(task.task_id)
            task_type = str(task.task_type)
            task_data = task.task_data

            if task_type == "cycle":
                with CYCLIC_TASKS_LOCK:
                    CYCLIC_TASKS[task_id] = task_data
                print(f"[scheduler] 已恢复循环任务: {task_id}")
            elif task_type == "sequence":
                with SEQUENCE_TASKS_LOCK:
                    SEQUENCE_TASKS[task_id] = task_data
                print(f"[scheduler] 已恢复顺序任务: {task_id}")
    finally:
        db.close()
    print("[scheduler] 任务状态恢复完成。")


def schedule_task(sn: str, do_name: str, value: int, delay_minutes: int) -> str:
    """
    安排一个一次性的DO控制任务。
    :param sn: 设备SN
    :param do_name: DO名称
    :param value: 1为闭合, 0为断开
    :param delay_minutes: 延迟执行的分钟数
    :return: 任务的唯一ID
    """
    job_id = f"task_{uuid.uuid4().hex[:8]}"
    run_date = datetime.datetime.now() + datetime.timedelta(minutes=delay_minutes)

    scheduler.add_job(
        _execute_task,
        "date",
        run_date=run_date,
        args=[sn, do_name, value, job_id],
        id=job_id,
    )

    task_info = {
        "job_id": job_id,
        "sn": sn,
        "do_name": do_name,
        "value": value,
        "status": "pending",
        "execute_at": run_date.strftime("%Y-%m-%d %H:%M:%S"),
        "scheduled_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }
    with TASKS_LOCK:
        SCHEDULED_TASKS[job_id] = task_info

    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [scheduler] 已安排新任务 {job_id}，将在 {run_date} 执行。")

    return job_id


# -- 新增安排循环任务函数 --
def schedule_cyclic_task(
    db: Session, sn: str, do_name: str, on_minutes: int, off_minutes: int
) -> str:
    """
    安排一个周期性的DO控制任务 (开->关->开->...)。
    任务会立即将DO置于'on'状态，然后开始循环。
    :param sn: 设备SN
    :param do_name: DO名称
    :param on_minutes: 每次保持开启的分钟数
    :param off_minutes: 每次保持关闭的分钟数
    :return: 循环任务的唯一ID
    """
    # 1. 创建任务ID
    cycle_id = f"cycle_{uuid.uuid4().hex[:8]}"
    on_job_id = f"{cycle_id}_on"
    off_job_id = f"{cycle_id}_off"
    total_interval_minutes = on_minutes + off_minutes

    # 2. 安排"开启"作业
    # 它会立即执行一次，然后按总周期循环
    scheduler.add_job(
        _execute_cyclic_task,
        "interval",
        minutes=total_interval_minutes,
        args=[sn, do_name, 1],
        id=on_job_id,
    )

    # 3. 安排"关闭"作业
    # 它会在'on_minutes'分钟后首次执行，然后按总周期循环
    first_off_run_time = datetime.datetime.now() + datetime.timedelta(
        minutes=on_minutes
    )
    scheduler.add_job(
        _execute_cyclic_task,
        "interval",
        minutes=total_interval_minutes,
        start_date=first_off_run_time,
        args=[sn, do_name, 0],
        id=off_job_id,
    )

    # 4. 存储循环任务的元数据
    cycle_info = {
        "cycle_id": cycle_id,
        "sn": sn,
        "do_name": do_name,
        "on_minutes": on_minutes,
        "off_minutes": off_minutes,
        "status": "active",
        "on_job_id": on_job_id,
        "off_job_id": off_job_id,
        "scheduled_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }
    with CYCLIC_TASKS_LOCK:
        CYCLIC_TASKS[cycle_id] = cycle_info

    # 持久化元数据
    crud.create_task_metadata(
        db, task_id=cycle_id, task_type="cycle", metadata=cycle_info
    )

    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [scheduler] 已安排新循环任务 {cycle_id}。")
    return cycle_id


# -- 新增结束 --


# -- 新增顺序循环任务 --
def schedule_sequence_task(
    db: Session,
    sn: str,
    do_a_name: str,
    do_a_minutes: int,
    do_b_name: str,
    do_b_minutes: int,
) -> str:
    """
    安排一个顺序循环任务 (A运行 -> B运行 -> A运行 ...)。
    :param sn: 设备SN
    :param do_a_name: 第一个DO的名称
    :param do_a_minutes: 第一个DO的运行分钟数
    :param do_b_name: 第二个DO的名称
    :param do_b_minutes: 第二个DO的运行分钟数
    :return: 顺序任务的唯一ID
    """
    # 1. 创建ID和计算总周期
    sequence_id = f"seq_{uuid.uuid4().hex[:8]}"
    job_a_on_id = f"{sequence_id}_a_on"
    job_a_off_id = f"{sequence_id}_a_off"
    job_b_on_id = f"{sequence_id}_b_on"
    job_b_off_id = f"{sequence_id}_b_off"
    total_interval_minutes = do_a_minutes + do_b_minutes
    now = datetime.datetime.now()

    # 2. 安排作业1: 开启A (立即执行, 后续按总周期循环)
    scheduler.add_job(
        _execute_cyclic_task,
        "interval",
        minutes=total_interval_minutes,
        start_date=now,
        args=[sn, do_a_name, 1],
        id=job_a_on_id,
    )

    # 3. 安排作业2: 关闭A (do_a_minutes后执行, 后续按总周期循环)
    start_a_off = now + datetime.timedelta(minutes=do_a_minutes)
    scheduler.add_job(
        _execute_cyclic_task,
        "interval",
        minutes=total_interval_minutes,
        start_date=start_a_off,
        args=[sn, do_a_name, 0],
        id=job_a_off_id,
    )

    # 4. 安排作业3: 开启B (与关闭A同时执行, 后续按总周期循环)
    scheduler.add_job(
        _execute_cyclic_task,
        "interval",
        minutes=total_interval_minutes,
        start_date=start_a_off,  # 与关闭A的时间点相同
        args=[sn, do_b_name, 1],
        id=job_b_on_id,
    )

    # 5. 安排作业4: 关闭B (总周期结束后执行, 后续按总周期循环)
    start_b_off = now + datetime.timedelta(minutes=total_interval_minutes)
    scheduler.add_job(
        _execute_cyclic_task,
        "interval",
        minutes=total_interval_minutes,
        start_date=start_b_off,
        args=[sn, do_b_name, 0],
        id=job_b_off_id,
    )

    # 6. 存储任务元数据
    sequence_info = {
        "sequence_id": sequence_id,
        "sn": sn,
        "do_a_name": do_a_name,
        "do_a_minutes": do_a_minutes,
        "do_b_name": do_b_name,
        "do_b_minutes": do_b_minutes,
        "status": "active",
        "job_ids": [job_a_on_id, job_a_off_id, job_b_on_id, job_b_off_id],
        "scheduled_at": now.strftime("%Y-%m-%d %H:%M:%S"),
    }
    with SEQUENCE_TASKS_LOCK:
        SEQUENCE_TASKS[sequence_id] = sequence_info

    # 持久化元数据
    crud.create_task_metadata(
        db,
        task_id=sequence_id,
        task_type="sequence",
        metadata=sequence_info,
    )

    current_time = now.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [scheduler] 已安排新顺序循环任务 {sequence_id}。")
    return sequence_id


def get_sequence_tasks() -> dict:
    """
    获取所有当前活动的顺序循环任务列表。
    """
    with SEQUENCE_TASKS_LOCK:
        return SEQUENCE_TASKS.copy()


def check_sequence_task_health(sequence_id: str) -> dict:
    """
    检查顺序循环任务的健康状态
    :param sequence_id: 顺序任务ID
    :return: 包含健康状态信息的字典
    """
    with SEQUENCE_TASKS_LOCK:
        sequence_info = SEQUENCE_TASKS.get(sequence_id)
        
    if not sequence_info:
        return {
            "healthy": False,
            "reason": "任务不存在于内存中",
            "missing_jobs": [],
            "total_jobs": 0
        }
    
    job_ids = sequence_info.get("job_ids", [])
    missing_jobs = []
    
    for job_id in job_ids:
        if not scheduler.get_job(job_id):
            missing_jobs.append(job_id)
    
    healthy = len(missing_jobs) == 0
    
    return {
        "healthy": healthy,
        "reason": "正常" if healthy else f"缺失{len(missing_jobs)}个作业",
        "missing_jobs": missing_jobs,
        "total_jobs": len(job_ids),
        "sequence_info": sequence_info
    }


def repair_sequence_task(db: Session, sequence_id: str) -> bool:
    """
    修复损坏的顺序循环任务
    :param db: 数据库会话
    :param sequence_id: 顺序任务ID
    :return: 修复是否成功
    """
    health_check = check_sequence_task_health(sequence_id)
    
    if health_check["healthy"]:
        return True
        
    print(f"[scheduler] 检测到顺序任务 {sequence_id} 异常，开始修复...")
    
    with SEQUENCE_TASKS_LOCK:
        sequence_info = SEQUENCE_TASKS.get(sequence_id)
        
    if not sequence_info:
        print(f"[scheduler] 无法修复任务 {sequence_id}：任务信息不存在")
        return False
    
    try:
        # 1. 先清理所有可能存在的旧作业
        for job_id in sequence_info.get("job_ids", []):
            try:
                scheduler.remove_job(job_id)
            except JobLookupError:
                pass
        
        # 2. 重新创建任务
        sn = sequence_info["sn"]
        do_a_name = sequence_info["do_a_name"]
        do_a_minutes = sequence_info["do_a_minutes"]
        do_b_name = sequence_info["do_b_name"]
        do_b_minutes = sequence_info["do_b_minutes"]
        
        # 取消当前任务
        cancel_sequence_task(db, sequence_id)
        
        # 重新创建任务
        new_sequence_id = schedule_sequence_task(
            db, sn, do_a_name, do_a_minutes, do_b_name, do_b_minutes
        )
        
        print(f"[scheduler] 顺序任务 {sequence_id} 修复完成，新任务ID: {new_sequence_id}")
        return True
        
    except Exception as e:
        print(f"[scheduler] 修复顺序任务 {sequence_id} 失败: {e}")
        return False


def cancel_sequence_task(db: Session, sequence_id: str) -> bool:
    """
    根据ID取消整个顺序循环任务链。
    """
    with SEQUENCE_TASKS_LOCK:
        sequence_info = SEQUENCE_TASKS.pop(sequence_id, None)

    if not sequence_info:
        return False

    # 从数据库删除元数据
    crud.delete_task_metadata(db, task_id=sequence_id)

    try:
        # 移除所有关联的作业
        for job_id in sequence_info.get("job_ids", []):
            try:
                scheduler.remove_job(job_id)
            except JobLookupError:
                pass  # 如果作业已不存在，则忽略

        # 安全措施：取消后，立即发送关闭指令给两个DO
        print(f"[scheduler] 顺序任务 {sequence_id} 取消, 发送安全关闭指令。")
        cmd_a = create_do_command(do_name=sequence_info["do_a_name"], value=0)
        send_command_to_device(sn=sequence_info["sn"], command=cmd_a)
        cmd_b = create_do_command(do_name=sequence_info["do_b_name"], value=0)
        send_command_to_device(sn=sequence_info["sn"], command=cmd_b)

        print(f"[scheduler] 顺序任务 {sequence_id} 已成功取消并停止。")
        return True
    except Exception as e:
        print(f"[scheduler] 取消顺序任务 {sequence_id} 时发生错误: {e}")
        # 如果出错，把信息加回去，方便排查
        with SEQUENCE_TASKS_LOCK:
            SEQUENCE_TASKS[sequence_id] = sequence_info
        return False


# -- 顺序循环任务结束 --


def get_scheduled_tasks() -> dict:
    """
    获取所有当前已安排的、尚未执行的任务列表。
    """
    with TASKS_LOCK:
        # 返回一个深拷贝以保证线程安全
        return SCHEDULED_TASKS.copy()


# -- 新增获取循环任务函数 --
def get_cyclic_tasks() -> dict:
    """
    获取所有当前活动的循环任务列表。
    """
    with CYCLIC_TASKS_LOCK:
        return CYCLIC_TASKS.copy()


# -- 新增结束 --


def cancel_task(job_id: str) -> bool:
    """
    根据任务ID取消一个已安排的任务。
    """
    with TASKS_LOCK:
        if job_id in SCHEDULED_TASKS:
            try:
                scheduler.remove_job(job_id)
                SCHEDULED_TASKS.pop(job_id)
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{current_time}] [scheduler] 任务 {job_id} 已成功取消。")
                return True
            except Exception as e:
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{current_time}] [scheduler] 取消任务 {job_id} 失败: {e}")
                return False
    return False


# -- 新增取消循环任务函数 --
def cancel_cyclic_task(db: Session, cycle_id: str) -> bool:
    """
    根据循环ID取消整个循环任务（包括'on'和'off'两个作业）。
    同时会立即发送一个关闭指令以确保设备处于安全状态。
    """
    with CYCLIC_TASKS_LOCK:
        cycle_info = CYCLIC_TASKS.pop(cycle_id, None)

    if not cycle_info:
        return False

    # 从数据库删除元数据
    crud.delete_task_metadata(db, task_id=cycle_id)

    on_job_id = cycle_info.get("on_job_id")
    off_job_id = cycle_info.get("off_job_id")

    try:
        # 尝试移除两个关联的作业
        if on_job_id:
            scheduler.remove_job(on_job_id)
        if off_job_id:
            scheduler.remove_job(off_job_id)

        # 安全措施：取消任务后，立即发送一个关闭指令
        print(f"[scheduler] 循环任务 {cycle_id} 取消, 发送安全关闭指令。")
        command = create_do_command(do_name=cycle_info["do_name"], value=0)
        send_command_to_device(sn=cycle_info["sn"], command=command)

        print(f"[scheduler] 循环任务 {cycle_id} 已成功取消并停止。")
        return True
    except Exception as e:
        print(f"[scheduler] 取消循环任务 {cycle_id} 时发生错误: {e}")
        # 如果出错，把信息加回去，方便排查
        with CYCLIC_TASKS_LOCK:
            CYCLIC_TASKS[cycle_id] = cycle_info
        return False


# -- 新增结束 --


def start_scheduler():
    """
    启动调度器。
    """
    # 在启动调度器之前，先从数据库恢复状态
    _restore_state_from_db()
    print("[scheduler] 任务调度服务已启动...")
    scheduler.start()

# /f:/水利站/backend/data_store.py
import threading

# 这是一个线程安全的全局数据存储模块。
# TCP服务器将数据写入这里，FastAPI服务器从这里读取数据。

# 使用字典来存储最新的设备数据，以设备SN为键
# 示例:
# {
#     "02801925060700002997": {
#         "float_switches": {"float1": 0, "float2": 0},
#         "water_pump1": {"auto_status": 0, ...},
#         "dianliucaiji2": {"curr2_ch1": 0.015, ...},
#         ...
#         "last_updated": "2025-07-09 08:32:31"
#     }
# }
DEVICE_DATA = {}

# 创建一个线程锁，用于保护对DEVICE_DATA的并发访问
# 在任何线程修改或读取DEVICE_DATA之前，都必须先获取这个锁
DATA_LOCK = threading.Lock()

# 使用字典来存储所有已连接的客户端socket，以设备SN为键
# 这使得API服务器可以根据SN找到对应的连接，并发送指令
# 结构: {"sn": {"socket": client_socket, "address": addr}}
CONNECTED_CLIENTS = {}

# 创建一个专门的锁来保护对CONNECTED_CLIENTS的并发访问
CLIENTS_LOCK = threading.Lock()

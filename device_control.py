# /f:/水利站/backend/device_control.py
import json
import datetime
import socket

from data_store import CONNECTED_CLIENTS, CLIENTS_LOCK
from database import SessionLocal
import crud


def create_do_command(do_name: str, value: int) -> str:
    """
    创建控制单个DO的JSON格式指令。
    :param do_name: DO的名称, e.g., "DO21", "DO22".
    :param value: 0 表示断开, 1 表示闭合.
    :return: JSON格式的指令字符串.
    """
    command = {
        "rw_prot": {
            "Ver": "1.0.1",
            "dir": "down",
            "id": "123",  # id可以是一个随机值或固定值
            "w_data": [{"name": do_name, "value": str(value)}],
        }
    }
    return json.dumps(command, ensure_ascii=False)


def send_command_to_device(sn: str, command: str) -> tuple[bool, int | None]:
    """
    通过已建立的TCP连接向指定设备发送指令，并记录操作日志。
    :param sn: 设备序列号.
    :param command: 要发送的完整指令字符串.
    :return: (成功状态, 日志记录ID)
    """
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 创建操作日志记录
    db = SessionLocal()
    try:
        log_entry = crud.create_operation_log(
            db=db,
            operation_type="device_command",
            operation_details=f"向设备 {sn} 发送控制指令",
            device_sn=sn,
            command_sent=command,
            execution_status="pending"
        )
        log_id = log_entry.id
    except Exception as e:
        print(f"[{current_time}] [send_command] 创建日志失败: {e}")
        log_id = None
    finally:
        db.close()
    
    with CLIENTS_LOCK:
        client_info = CONNECTED_CLIENTS.get(sn)

    if not client_info:
        error_msg = f"设备 {sn} 未连接"
        print(f"[{current_time}] [send_command] 失败: {error_msg}")
        if log_id:
            db = SessionLocal()
            try:
                crud.update_operation_log_status(db, log_id, "failed", error_msg)
            finally:
                db.close()
        return False, log_id

    client_socket = client_info.get("socket")
    if not client_socket:
        error_msg = f"设备 {sn} 的socket无效"
        print(f"[{current_time}] [send_command] 失败: {error_msg}")
        if log_id:
            db = SessionLocal()
            try:
                crud.update_operation_log_status(db, log_id, "failed", error_msg)
            finally:
                db.close()
        return False, log_id

    try:
        print(f"[{current_time}] [send_command] 正在向设备 {sn} 发送指令: {command}")
        client_socket.sendall(command.encode("utf-8"))
        print(f"[{current_time}] [send_command] 指令发送成功。")
        
        # 更新日志状态为成功
        if log_id:
            db = SessionLocal()
            try:
                crud.update_operation_log_status(db, log_id, "success")
            finally:
                db.close()
        
        return True, log_id
    except socket.error as e:
        error_msg = f"socket错误: {e}"
        print(f"[{current_time}] [send_command] 发送指令失败: {error_msg}")
        
        # 连接失效时，从连接池中移除该设备
        with CLIENTS_LOCK:
            removed_client = CONNECTED_CLIENTS.pop(sn, None)
            if removed_client:
                print(f"[{current_time}] [send_command] 设备 {sn} 连接失效，已从连接池中移除")
        
        if log_id:
            db = SessionLocal()
            try:
                crud.update_operation_log_status(db, log_id, "failed", error_msg)
            finally:
                db.close()
        return False, log_id
    except Exception as e:
        error_msg = f"未知错误: {e}"
        print(f"[{current_time}] [send_command] 发送指令时发生未知错误: {error_msg}")
        if log_id:
            db = SessionLocal()
            try:
                crud.update_operation_log_status(db, log_id, "failed", error_msg)
            finally:
                db.close()
        return False, log_id

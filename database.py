# /f:/水利站/backend/database.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# 定义SQLite数据库文件的路径。
# "sqlite:///./sql_app.db" 表示在项目根目录下创建一个名为 sql_app.db 的文件。
SQLALCHEMY_DATABASE_URL = "sqlite:///./sql_app.db"

# 创建SQLAlchemy引擎
# connect_args={"check_same_thread": False} 是SQLite特有的配置，允许在多线程中使用。
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)

# 创建一个数据库会话类（SessionLocal），用于后续的数据库操作实例。
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建一个Base类，我们之后创建的所有ORM模型（数据库表）都将继承这个类。
Base = declarative_base()


def create_db_and_tables():
    """
    根据我们定义的模型，在数据库中创建所有表。
    如果表已存在，则不会重复创建。
    """
    print("[database] 正在创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("[database] 数据库表创建完成。")

# /f:/水利站/backend/pump_control_alternating.py
"""
新的水泵和气泵控制逻辑：
1. 水泵交替启动：浮球激活时，轮流启动水泵1或水泵2
2. 故障检测和切换：当一个泵故障时，自动切换到另一个泵
3. 气泵故障保护：一个气泵关闭时，另一个气泵自动运行
"""
import datetime
from sqlalchemy.orm import Session
import crud
from data_store import DATA_LOCK, DEVICE_DATA
from device_control import create_do_command, send_command_to_device
from device_timing import wait_for_device_ready


def detect_pump_failure(device_data: dict, pump_name: str) -> bool:
    """
    检测水泵是否故障
    根据设备数据判断水泵是否正常工作
    """
    # 获取水泵的运行状态和电流信息
    pump_data = device_data.get(pump_name, {})
    
    # 检查水泵是否应该运行但实际未运行
    if pump_name == "water_pump1":
        do_status = device_data.get("DO21_status")
        expected_running = do_status == 1
    elif pump_name == "water_pump2":
        do_status = device_data.get("DO22_status")
        expected_running = do_status == 1
    elif pump_name == "air_pump1":
        do_status = device_data.get("DO23_status")
        expected_running = do_status == 1
    elif pump_name == "air_pump2":
        do_status = device_data.get("DO24_status")
        expected_running = do_status == 1
    else:
        return False
    
    if not expected_running:
        return False  # 如果泵本来就不应该运行，不算故障
    
    # 检查电流值 - 如果DO状态为1但没有电流，可能是故障
    current_value = device_data.get("dianliucaiji2", 0)
    
    # 如果泵应该运行但电流值为0或异常低，判断为故障
    if expected_running and current_value < 0.1:  # 电流阈值可配置
        return True
    
    # 还可以添加其他故障判断条件，如温度、振动等
    
    return False


def get_next_water_pump(db: Session, sn: str) -> str:
    """
    获取下一个应该启动的水泵（交替逻辑）
    """
    last_pump_key = f"last_water_pump_{sn}"
    last_pump = crud.get_kv(db, last_pump_key) or "water_pump2"  # 默认从water_pump2开始，这样第一次会选择water_pump1
    
    # 交替选择
    if last_pump == "water_pump1":
        next_pump = "water_pump2"
    else:
        next_pump = "water_pump1"
    
    # 保存选择结果
    crud.set_kv(db, last_pump_key, next_pump)
    
    return next_pump


def get_available_water_pump(db: Session, sn: str, device_data: dict, preferred_pump: str = None) -> str:
    """
    获取可用的水泵，考虑故障状态
    """
    if preferred_pump:
        # 检查首选泵是否可用
        if not detect_pump_failure(device_data, preferred_pump):
            auto_status = device_data.get(preferred_pump, {}).get("auto_status")
            if auto_status == 1:
                return preferred_pump
    
    # 检查所有水泵的可用性
    pumps = ["water_pump1", "water_pump2"]
    for pump in pumps:
        if not detect_pump_failure(device_data, pump):
            auto_status = device_data.get(pump, {}).get("auto_status")
            if auto_status == 1:
                return pump
    
    return None  # 没有可用的水泵


def get_available_air_pump(db: Session, sn: str, device_data: dict) -> str:
    """
    获取可用的气泵，优先选择非故障的
    """
    pumps = ["air_pump1", "air_pump2"]
    for pump in pumps:
        if not detect_pump_failure(device_data, pump):
            auto_status = device_data.get(pump, {}).get("auto_status")
            if auto_status == 1:
                return pump
    
    return None  # 没有可用的气泵


def handle_water_pump_alternating_logic(db: Session, sn: str, new_data: dict):
    """
    处理水泵交替启动逻辑
    """
    if "float_switches" not in new_data or "float1" not in new_data["float_switches"]:
        return
    
    current_float1 = new_data["float_switches"]["float1"]
    
    with DATA_LOCK:
        previous_float1 = (
            DEVICE_DATA.get(sn, {}).get("float_switches", {}).get("float1", None)
        )
    
    current_time = datetime.datetime.now()
    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 定义数据库键
    active_pump_key = f"active_water_pump_{sn}"
    start_time_key = f"water_pump_start_time_{sn}"
    
    # Case 1: 浮球复位 (1 -> 0)，关闭所有水泵
    if current_float1 == 0 and previous_float1 == 1:
        print(f"[{current_time_str}] [pump_alternating] 浮球 '{sn}' 复位，关闭所有水泵")
        
        # 记录操作日志
        pump_log = crud.create_operation_log(
            db=db,
            operation_type="pump_control",
            operation_details=f"浮球复位，关闭所有水泵",
            device_sn=sn,
            additional_data={
                "trigger": "float_reset",
                "float1_status": current_float1
            }
        )
        
        # 关闭所有水泵
        for pump_name, do_name in [("water_pump1", "DO21"), ("water_pump2", "DO22")]:
            if new_data.get(f"{do_name}_status") == 1:
                wait_for_device_ready(sn)
                command = create_do_command(do_name=do_name, value=0)
                success, _ = send_command_to_device(sn=sn, command=command)
                print(f"[{current_time_str}] [pump_alternating] {pump_name}停止指令发送{'成功' if success else '失败'}")
        
        # 清理状态
        crud.set_kv(db, active_pump_key, "")
        crud.set_kv(db, start_time_key, "")
        crud.update_operation_log_status(db, pump_log.id, "success")
        return
    
    # Case 2: 浮球激活 (0 -> 1 或 None -> 1)，启动水泵
    if current_float1 == 1 and previous_float1 in [0, None]:
        print(f"[{current_time_str}] [pump_alternating] 浮球 '{sn}' 激活，选择水泵启动")
        
        # 获取下一个应该启动的水泵（交替逻辑）
        preferred_pump = get_next_water_pump(db, sn)
        
        # 获取实际可用的水泵（考虑故障状态）
        available_pump = get_available_water_pump(db, sn, new_data, preferred_pump)
        
        if not available_pump:
            print(f"[{current_time_str}] [pump_alternating] 没有可用的水泵")
            return
        
        # 确定DO名称
        do_name = "DO21" if available_pump == "water_pump1" else "DO22"
        
        # 记录操作日志
        pump_log = crud.create_operation_log(
            db=db,
            operation_type="pump_control",
            operation_details=f"浮球激活，启动{available_pump} ({do_name})",
            device_sn=sn,
            additional_data={
                "pump_name": available_pump,
                "do_name": do_name,
                "trigger": "float_activation",
                "float1_status": current_float1,
                "preferred_pump": preferred_pump,
                "is_failover": available_pump != preferred_pump
            }
        )
        
        # 启动水泵
        wait_for_device_ready(sn)
        command = create_do_command(do_name=do_name, value=1)
        success, _ = send_command_to_device(sn=sn, command=command)
        
        if success:
            # 记录状态
            crud.set_kv(db, active_pump_key, available_pump)
            crud.set_kv(db, start_time_key, current_time.isoformat())
            crud.update_operation_log_status(db, pump_log.id, "success")
            print(f"[{current_time_str}] [pump_alternating] {available_pump}启动成功")
        else:
            crud.update_operation_log_status(db, pump_log.id, "failed", "设备命令发送失败")
            print(f"[{current_time_str}] [pump_alternating] {available_pump}启动失败")
        return
    
    # Case 3: 浮球保持激活 (1 -> 1)，检查当前运行的水泵是否故障
    if current_float1 == 1 and previous_float1 == 1:
        current_active_pump = crud.get_kv(db, active_pump_key)
        
        if current_active_pump:
            # 检查当前运行的水泵是否故障
            if detect_pump_failure(new_data, current_active_pump):
                print(f"[{current_time_str}] [pump_alternating] 检测到{current_active_pump}故障，切换到备用水泵")
                
                # 获取备用水泵
                backup_pump = "water_pump2" if current_active_pump == "water_pump1" else "water_pump1"
                available_pump = get_available_water_pump(db, sn, new_data, backup_pump)
                
                if available_pump and available_pump != current_active_pump:
                    # 关闭故障泵
                    failed_do = "DO21" if current_active_pump == "water_pump1" else "DO22"
                    wait_for_device_ready(sn)
                    stop_command = create_do_command(do_name=failed_do, value=0)
                    send_command_to_device(sn=sn, command=stop_command)
                    
                    # 启动备用泵
                    backup_do = "DO21" if available_pump == "water_pump1" else "DO22"
                    wait_for_device_ready(sn)
                    start_command = create_do_command(do_name=backup_do, value=1)
                    success, _ = send_command_to_device(sn=sn, command=start_command)
                    
                    if success:
                        # 更新状态
                        crud.set_kv(db, active_pump_key, available_pump)
                        crud.set_kv(db, start_time_key, current_time.isoformat())
                        
                        # 记录故障切换日志
                        crud.create_operation_log(
                            db=db,
                            operation_type="pump_failover",
                            operation_details=f"水泵故障切换：{current_active_pump} -> {available_pump}",
                            device_sn=sn,
                            additional_data={
                                "failed_pump": current_active_pump,
                                "backup_pump": available_pump,
                                "trigger": "pump_failure_detection"
                            }
                        )
                        
                        print(f"[{current_time_str}] [pump_alternating] 故障切换成功：{current_active_pump} -> {available_pump}")


def handle_air_pump_logic(db: Session, sn: str, new_data: dict):
    """
    处理气泵控制逻辑
    当一个气泵关闭时，另一个气泵应该自动运行
    """
    current_time = datetime.datetime.now()
    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 获取气泵状态
    air_pump1_status = new_data.get("DO23_status", 0)
    air_pump2_status = new_data.get("DO24_status", 0)
    
    # 检查气泵故障状态
    air_pump1_failed = detect_pump_failure(new_data, "air_pump1")
    air_pump2_failed = detect_pump_failure(new_data, "air_pump2")
    
    # 检查自动模式状态
    air_pump1_auto = new_data.get("air_pump1", {}).get("auto_status") == 1
    air_pump2_auto = new_data.get("air_pump2", {}).get("auto_status") == 1
    
    active_air_pump_key = f"active_air_pump_{sn}"
    current_active_air_pump = crud.get_kv(db, active_air_pump_key)
    
    # 逻辑1：如果一个气泵关闭且另一个可用，启动另一个
    if air_pump1_status == 0 and air_pump2_status == 0:
        # 两个气泵都关闭，选择一个启动
        if air_pump1_auto and not air_pump1_failed:
            target_pump = "air_pump1"
            target_do = "DO23"
        elif air_pump2_auto and not air_pump2_failed:
            target_pump = "air_pump2"
            target_do = "DO24"
        else:
            return  # 没有可用的气泵
        
        print(f"[{current_time_str}] [air_pump] 启动气泵 {target_pump}")
        
        wait_for_device_ready(sn)
        command = create_do_command(do_name=target_do, value=1)
        success, _ = send_command_to_device(sn=sn, command=command)
        
        if success:
            crud.set_kv(db, active_air_pump_key, target_pump)
            crud.create_operation_log(
                db=db,
                operation_type="air_pump_control",
                operation_details=f"启动气泵 {target_pump}",
                device_sn=sn,
                additional_data={"pump_name": target_pump, "do_name": target_do}
            )
    
    # 逻辑2：如果当前运行的气泵故障，切换到另一个
    elif current_active_air_pump:
        if current_active_air_pump == "air_pump1" and air_pump1_failed:
            if air_pump2_auto and not air_pump2_failed:
                print(f"[{current_time_str}] [air_pump] 气泵1故障，切换到气泵2")
                
                # 关闭气泵1
                wait_for_device_ready(sn)
                stop_command = create_do_command(do_name="DO23", value=0)
                send_command_to_device(sn=sn, command=stop_command)
                
                # 启动气泵2
                wait_for_device_ready(sn)
                start_command = create_do_command(do_name="DO24", value=1)
                success, _ = send_command_to_device(sn=sn, command=start_command)
                
                if success:
                    crud.set_kv(db, active_air_pump_key, "air_pump2")
                    crud.create_operation_log(
                        db=db,
                        operation_type="air_pump_failover",
                        operation_details="气泵故障切换：air_pump1 -> air_pump2",
                        device_sn=sn,
                        additional_data={
                            "failed_pump": "air_pump1",
                            "backup_pump": "air_pump2"
                        }
                    )
        
        elif current_active_air_pump == "air_pump2" and air_pump2_failed:
            if air_pump1_auto and not air_pump1_failed:
                print(f"[{current_time_str}] [air_pump] 气泵2故障，切换到气泵1")
                
                # 关闭气泵2
                wait_for_device_ready(sn)
                stop_command = create_do_command(do_name="DO24", value=0)
                send_command_to_device(sn=sn, command=stop_command)
                
                # 启动气泵1
                wait_for_device_ready(sn)
                start_command = create_do_command(do_name="DO23", value=1)
                success, _ = send_command_to_device(sn=sn, command=start_command)
                
                if success:
                    crud.set_kv(db, active_air_pump_key, "air_pump1")
                    crud.create_operation_log(
                        db=db,
                        operation_type="air_pump_failover",
                        operation_details="气泵故障切换：air_pump2 -> air_pump1",
                        device_sn=sn,
                        additional_data={
                            "failed_pump": "air_pump2",
                            "backup_pump": "air_pump1"
                        }
                    )


def handle_alternating_pump_logic(db: Session, sn: str, new_data: dict):
    """
    主控制函数，处理新的交替启动和故障切换逻辑
    """
    # 处理水泵逻辑
    handle_water_pump_alternating_logic(db, sn, new_data)
    
    # 处理气泵逻辑
    handle_air_pump_logic(db, sn, new_data)
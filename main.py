# /f:/水利站/backend/main.py
import threading
import uvicorn
import datetime

# 从对应模块导入TCP服务器的启动函数和FastAPI应用实例
from generic_tcp_server import start_server
from api_server import app

# 导入数据库创建函数
from database import create_db_and_tables, SessionLocal

# 导入任务调度服务启动函数
from scheduler_service import start_scheduler

# 导入状态监控服务
from state_monitor import start_state_monitor

# 新增导入
import crud


def _initialize_system_state():
    """
    在服务启动时，检查并初始化系统关键状态。
    新水泵控制逻辑：water_pump1为主力泵，不需要轮换状态
    """
    db = SessionLocal()
    try:
        print("[main] 系统启动完成，使用新的水泵控制逻辑（water_pump1为主力泵）")
    finally:
        db.close()


def run_tcp_server():
    """在一个独立的线程中运行TCP服务器"""
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [main] 准备在后台线程启动TCP服务器...")
    start_server()


if __name__ == "__main__":
    # 在服务启动前，执行数据库初始化
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [main] 初始化数据库...")
    create_db_and_tables()
    print(f"[{current_time}] [main] 数据库初始化完成。")

    # 新增调用: 初始化系统状态
    _initialize_system_state()

    # 启动后台任务调度器
    start_scheduler()

    # 启动状态监控守护进程
    start_state_monitor()

    # 创建并启动TCP服务器的后台线程
    # 设置为守护线程(daemon=True)，这样当主程序(Uvicorn)退出时，该线程也会被自动终止
    tcp_server_thread = threading.Thread(target=run_tcp_server, daemon=True)
    tcp_server_thread.start()

    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [main] TCP服务器已在后台启动。")
    print(f"[{current_time}] [main] 准备启动FastAPI服务器...")

    # 在主线程中启动Uvicorn来运行FastAPI应用
    # Uvicorn是一个ASGI服务器，用于生产环境部署FastAPI应用
    # host="0.0.0.0" 表示监听所有网络接口，服务可以被局域网或公网访问
    # port=8500 是API服务的端口
    # reload=True 会在代码变动后自动重启服务，方便开发调试 (生产环境建议关闭)
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8500,
    )

    print(f"[{current_time}] [main] FastAPI服务器已启动。")
    print("=" * 60)
    print("服务已启动！")
    print(f"  - TCP服务器正在监听端口: 8889")
    print(f"  - API服务器正在运行于: http://0.0.0.0:8500")
    print(f"  - 查看实时数据请访问: http://127.0.0.1:8500/data")
    print(f"  - 查看历史数据请访问: http://127.0.0.1:8500/history/YOUR_DEVICE_SN")
    print(f"  - 查看API文档请访问: http://127.0.0.1:8500/docs")
    print("=" * 60)
